# !!! 这里不仅仅是配置示例
# 任何系统中要添加的配置项必须在这里声明出来, 并建议给一个示例值, 加上注释
# 这里的值系统在启动时会进行必选值检查
# IDEServer 连接参数设置
PAAS_DOMAIN_URL="https://www.clackypaas.com"
PAAS_TENANT_CODE="demo"

# 开发模式
IDE_SERVER_DEBUG_OPTIONAL=False

# JWT验证, 根后端保持一致
AUTH_JWT_KEY_OPTIONAL=''
AUTH_JWT_SECRET_OPTIONAL=''


# IDEServer 函数调用超时时间
IDE_SERVER_FUNC_CALL_TIMEOUT_OPTIONAL="7"
# IDEServer ping超时时间单位秒
IDE_SERVER_PING_TIMEOUT_OPTIONAL="100"
# IDEServer ping间隔时间单位秒
IDE_SERVER_PING_INTERVAL_OPTIONAL="25"
# IDEServer syncPlaygroundInfo 接口超时时间7秒
IDE_SERVER_SYNC_PLAYGROUND_INFO_TIMEOUT_OPTIONAL="7"

# 自定义IDE服务器URL，设置后将优先使用此URL而非通过API获取URL
IDE_SERVER_URL_OPTIONAL=''

# LLM 参数配置
JINA_API_KEY_OPTIONAL=''

PROXY_BASE_URL_OPTIONAL=''
PROXY_SECRET_OPTIONAL=''

# strong 版本, 最强但最贵和慢, 用于核心推理
LLM_MODEL="ollama/llama3"
LLM_API_KEY="ollama"
LLM_BASE_URL="http://localhost:11434"
# normal 版本, 中等, 用于编码等
LLM_NORMAL_MODEL_OPTIONAL=""
LLM_NORMAL_API_KEY_OPTIONAL=""
LLM_NORMAL_BASE_URL_OPTIONAL=""

# LANGFUSE 配置
LANGFUSE_HOST_OPTIONAL=""
LANGFUSE_PUBLIC_KEY_OPTIONAL=""
LANGFUSE_SECRET_KEY_OPTIONAL=""
LANGFUSE_PROJECT_ID_OPTIONAL=""
# PROJECT 用于标签, 建议: 本地设开发者个人名字, 线上用分支名staging/production
LANGFUSE_PROJECT_OPTIONAL=""

# 日志等级: 'debug' or 'info'
LOG_LEVEL_OPTIONAL='debug'
# 日志文件
#LOG_FILE_OPTIONAL=""
# 日志记录的最大长度
LOG_RECODE_MAX_LENGTH_OPTIONAL='256'
# 是否拦截标准库日志
LOG_INTERCEPT_STDLIB_OPTIONAL=False

# 缓存层: REDIS 配置, HOST 为空时不启用
CACHE_REDIS_HOST_OPTIONAL=''
CACHE_REDIS_PORT_OPTIONAL=''
CACHE_REDIS_DB_OPTIONAL=''
# 过期多久删除, 默认为 30 天, 这里内部测试, 先定为 1 天
CACHE_REDIS_DAY_OPTIONAL='1'


# 这个是可选变量示例, 不要删除, 以 _OPTIONAL 结局系统不会强制检查配置
XXX_OPTIONAL=''

# 向量数据库的配置
ADMIN_VECTOR_DB_HOST_OPTIONAL=""
ADMIN_VECTOR_DB_PORT_OPTIONAL=""
ADMIN_VECTOR_DB_SPARSE_VECTOR_NAME_OPTIONAL="bm25"
ADMIN_VECTOR_DB_DENSE_VECTOR_NAME_OPTIONAL="text-embedding-3-small"
ADMIN_VECTOR_DB_DENSE_DIM_OPTIONAL="1536"
ADMIN_SPARSE_EMBEDDING_MODEL_OPTIONAL="Qdrant/bm25"
ADMIN_DENSE_EMBEDDING_MODEL_OPTIONAL="text-embedding-3-small"
ADMIN_VECTOR_DB_API_KEY_OPTIONAL=""

# 支付模块密钥
PAYMENT_KEY_OPTIONAL=''

# 本地图片传递功能开关, 设为 True 开启, False 关闭
ENABLE_IMAGE_REFERENCE_OPTIONAL=True
