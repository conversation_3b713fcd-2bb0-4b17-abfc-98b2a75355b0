import pytest
import aiohttp
import asyncio

from heracles.core.schema import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Message, ErrorHandlerMessageType, FileSnippet
from heracles.core.schema.task import ActionStatus
from heracles.core.schema.models import EnvironmentAnalysisResultModel, PRMessageModel, CommandResultModel, TerminalInteractResponseModel
from heracles.core.utils import mock_asyncio_wait_for
from heracles.core.exceptions import UserTaskStatusException, AgentRunException, \
    IDEServerConnectException, UserEventArgumentException, IDEServerFunCallTimeoutException, UserTaskAppendLimitException

from heracles.agent_controller.llm import LLM
from heracles.agent_roles.plan_role.create_test_task import create_test_task
from heracles.server.clacky.ide_server_client import IdeServerClient
from heracles.core.utils.redis_cache import redis_cache

@pytest.mark.asyncio
async def test_playground_null(create_playground_channel, mocker):
    playground_channel = await create_playground_channel
    playground_channel.playground_manager._reset()
    playground_channel._current_playground = None
    with pytest.raises(AgentRunException):
        _ = playground_channel.current_playground

    mocker.patch.object(IdeServerClient, 'connect', side_effect=IDEServerConnectException("mocked exception"))

    mocker.patch.object(playground_channel.logger, 'error')
    with pytest.raises(Exception, match="mocked exception"):
        await playground_channel.start()

@pytest.mark.asyncio
async def test_playground_on_message(create_playground_channel):
    playground_channel = await create_playground_channel
    res = await playground_channel.on_message("hello", memory_regenerate=False)
    await playground_channel.current_playground.agent_controller._asyncio_message
    assert res is True

    await playground_channel.on_message("hello2")
    res = await playground_channel.on_message("hello3", memory_regenerate=False)
    assert res is False
    if playground_channel.current_playground.agent_controller._asyncio_message:
        await playground_channel.current_playground.agent_controller._asyncio_message
    res = await playground_channel.on_message("hello4", memory_regenerate=False)
    await playground_channel.current_playground.agent_controller._asyncio_message
    assert res is True

    res = await playground_channel.on_message("hello4", memory_regenerate=True)
    await playground_channel.current_playground.agent_controller._asyncio_message
    assert res is True

    res = await playground_channel.on_message("hello5", memory_regenerate=False, need_consider_task=True)
    await playground_channel.current_playground.agent_controller._asyncio_message
    assert res is True

    res = await playground_channel.on_message("hello6", memory_regenerate=False, need_consider_task=False)
    await playground_channel.current_playground.agent_controller._asyncio_message
    assert res is True

    await playground_channel.broadcast_all('event1', "hello")

@pytest.mark.asyncio
async def test_playground_error_handler(create_playground_channel):
    playground_channel = await create_playground_channel
    error_message_content = 'on_message error'
    error_message = ErrorHandlerMessage(error_type=ErrorHandlerMessageType.MESSAGE, content=error_message_content)

    with pytest.raises(AgentRunException):
        await playground_channel.error_handler(error_message)

@pytest.mark.asyncio
async def test_playground_on_clacky_test(create_playground_channel, mocker):
    playground_channel = await create_playground_channel

    mock_asyncio_wait_for(mocker)

    await playground_channel.on_message("clacky file_tree")
    await playground_channel._asyncio_task

    await playground_channel.on_message("clacky123")
    await playground_channel._asyncio_task

    await playground_channel.on_message("clacky manager")
    await playground_channel._asyncio_task

    await playground_channel.on_message("clacky manager true")
    await playground_channel._asyncio_task

    await playground_channel.on_message("clacky manager clear")
    await playground_channel._asyncio_task

    await playground_channel.on_message("clacky cache id_1")
    await playground_channel._asyncio_task

    await playground_channel.on_message("clacky rag")
    await playground_channel._asyncio_task

    await playground_channel.on_message("clacky xxx")
    await playground_channel._asyncio_task

    await playground_channel.on_message("clacky terminal ls")
    await playground_channel._asyncio_task

    await playground_channel.on_message("clacky ai")
    await playground_channel._asyncio_task

@pytest.mark.asyncio
async def test_playground_start_task(create_playground_channel, mocker):
    playground_channel = await create_playground_channel
    mocker.patch.object(playground_channel.current_playground.agent_controller.plan_role, 'make_plan', return_value=create_test_task())

    # 未制定计划状态无法启动任务
    with pytest.raises(UserTaskStatusException):
        await playground_channel.on_u_start_task()

    # 第一次制定, 不会报错
    await playground_channel.on_u_make_plan('test:give me a plan')
    await playground_channel.current_playground.agent_controller._asyncio_task
    await playground_channel.on_u_reset_task()
    # 第二次制定, 仍然不会报错
    await playground_channel.on_u_make_plan('test:give me a plan2')
    # 让其运行完成
    await playground_channel.current_playground.agent_controller._asyncio_task
    # 制定计划后正常启动任务
    await playground_channel.on_u_start_task()

    # 可以继续暂停计划
    await playground_channel.on_u_pause_task()
    assert (
        playground_channel.current_playground.agent_controller.task_role.task_state.pausing.is_active
    )

    await playground_channel.on_u_resume_task()
    assert (
        playground_channel.current_playground.agent_controller.task_role.task_state.working.is_active
    )

    await playground_channel.on_u_cancel_task()
    assert (
        playground_channel.current_playground.agent_controller.task_role.task_state.canceled.is_active
    )

@pytest.mark.asyncio
async def test_playground_abnormal_task(create_playground_channel, mocker):
    playground_channel = await create_playground_channel
    mocker.patch.object(playground_channel.current_playground.ide_server_client, 'agent_terminal_with_result', return_value='ok')
    plan_role = playground_channel.current_playground.agent_controller.plan_role
    mocker.patch.object(plan_role, 'make_plan', return_value=create_test_task())
    mocker.patch.object(
        playground_channel.current_playground.agent_controller.summary_role,
        'run',
        return_value=None,
    )
    await playground_channel.on_u_make_plan('test:give me a plan')
    await playground_channel.current_playground.agent_controller._asyncio_task

    # 在planning状态时，可以追加step和action；不会变化状态
    step_id = await playground_channel.on_u_add_step({
        'title': 'title1',
        'task_actions': [{
            'action': 'add_file',
            'action_object': {
                'detailed_requirement': 'xxx',
                'path': 'xxx'
            }
        }]
    })
    await playground_channel.on_u_add_action(step_id, {
        'action': 'add_file',
        'action_object': {
            'detailed_requirement': 'xxx',
            'path': 'yyy'
        }
    })
    assert (
        playground_channel.current_playground.agent_controller.task_role.task_state.planning.is_active
    )

    await playground_channel.on_u_start_task()
    await asyncio.sleep(0.5)
    await playground_channel.on_u_pause_task()
    # 可以用 start 继续启动计划
    await playground_channel.on_u_start_task()
    await playground_channel.current_playground.agent_controller._asyncio_task
    assert (
        playground_channel.current_playground.agent_controller.task_role.task_state.done.is_active
    )
    step_id = await playground_channel.on_u_add_step({
        'title': 'title1',
        'task_actions': [{
            'action': 'add_file',
            'action_object': {
                'detailed_requirement': 'xxx',
                'path': 'xxx'
            }
        }]
    })
    action_id = await playground_channel.on_u_add_action(step_id, {
        'action': 'add_file',
        'action_object': {
            'detailed_requirement': 'xxx',
            'path': 'yyy'
        }
    })
    assert (
        playground_channel.current_playground.agent_controller.task_role.task_state.appended.is_active
    )
    task = playground_channel.current_playground.agent_controller.task
    assert task.find_action_by_id(action_id).status == ActionStatus.INIT_STATUS
    await playground_channel.on_u_start_task()
    await playground_channel.current_playground.agent_controller._asyncio_task
    assert task.find_action_by_id(action_id).status != ActionStatus.INIT_STATUS
    assert (
        playground_channel.current_playground.agent_controller.task_role.task_state.done.is_active
    )

@pytest.mark.asyncio
async def test_playground_make_spec(create_playground_channel, mocker):
    playground_channel = await create_playground_channel
    await playground_channel.on_u_make_spec('test:123')
    mock = mocker.patch.object(playground_channel.current_playground, 'trigger_spec_updated')
    await playground_channel.current_playground.agent_controller._asyncio_task
    mock.assert_called_once()

@pytest.mark.asyncio
async def test_playground_smart_detect(create_playground_channel, mocker):
    playground_channel = await create_playground_channel
    mocker.patch.object(playground_channel.current_playground.agent_controller.check_role, 'run_and_check_project', return_value=None)
    await playground_channel.on_u_enable_smart_detect()
    await playground_channel.on_u_enable_smart_detect()
    await playground_channel.on_u_disable_smart_detect()

@pytest.mark.asyncio
async def test_playground_analyze_project_environment(create_playground_channel, mocker):
    playground_channel = await create_playground_channel

    # 清空Redis缓存，避免使用之前的缓存数据
    analyze_role = playground_channel.current_playground.agent_controller.analyze_role
    cache_key = analyze_role.get_project_knowledge_key()
    redis_cache.delete(cache_key)

    mock = mocker.patch.object(aiohttp.ClientSession, 'request', return_value=mocker.MagicMock(
        __aenter__=mocker.AsyncMock(return_value=mocker.MagicMock(
            status=200,
            json=mocker.AsyncMock(return_value={
                "status": "success",
                "data": [
                    {
                        "id": "403710023303299078",
                        "name": "Express+Node.js 20",
                        "runtime": "node 20",
                        "packageManagers": [
                            "npm 10.7.0",
                            "pnpm 9.14.3",
                            "yarn 1.22.22"
                        ],
                        "language": "Node.js",
                        "tags": [
                            "Framework"
                        ]
                    },
                    {
                        "id": "403710023303299079",
                        "name": "Next+Node.js 20",
                        "runtime": "node 20",
                        "packageManagers": [
                            "npm 10.7.0",
                            "pnpm 9.14.3",
                            "yarn 1.22.22"
                        ],
                        "language": "Node.js",
                        "tags": [
                            "Framework"
                        ]
                    },
                    {
                        "id": "403710023458488320",
                        "name": "Node.js 18",
                        "runtime": "node 18",
                        "packageManagers": [
                            "npm 10.7.0",
                            "pnpm 9.14.3",
                            "yarn 1.22.22"
                        ],
                        "language": "HTML/CSS/JS",
                        "tags": [
                            "Pure Language"
                        ]
                    }
                ]
            }
        )),
        __aexit__=mocker.AsyncMock(return_value=None)
    )))
    mocker.patch.object(analyze_role, 'aask', return_value=EnvironmentAnalysisResultModel(
        message_type='info',
        reason='',
        instructions=[],
        environments=[],
        middlewares=[]
    ))
    await playground_channel.on_u_analyze_project_environment()
    # environment 和 middleware 各调用一次
    assert mock.call_count == 2

@pytest.mark.asyncio
async def test_playground_code_completion(create_playground_channel):
    playground_channel = await create_playground_channel
    await playground_channel.on_u_code_completion('path1', 'code snippet')

@pytest.mark.asyncio
async def test_playground_build_workspace(create_playground_channel):
    playground_channel = await create_playground_channel
    await playground_channel.on_u_build_workspace()

@pytest.mark.asyncio
async def test_playground_analyze_project_dependencies(create_playground_channel, mocker):
    playground_channel = await create_playground_channel
    mocker.patch.object(LLM, 'async_send', return_value='test')
    await playground_channel.on_u_build_workspace('project_dependencies')

@pytest.mark.asyncio
async def test_playground_analyze_project_components(create_playground_channel):
    playground_channel = await create_playground_channel
    await playground_channel.on_u_build_workspace('project_components')

@pytest.mark.asyncio
async def test_playground_analyze_project_structure(create_playground_channel):
    playground_channel = await create_playground_channel
    await playground_channel.on_u_build_workspace('project_structure')

@pytest.mark.asyncio
async def test_playground_reset_session(create_playground_channel):
    playground_channel = await create_playground_channel
    await playground_channel.on_u_reset_message_session()

@pytest.mark.asyncio
async def test_playground_stop_message(create_playground_channel):
    playground_channel = await create_playground_channel
    await playground_channel.on_u_stop_message()

@pytest.mark.asyncio
async def test_playground_any(create_playground_channel):
    playground_channel = await create_playground_channel
    with pytest.raises(AgentRunException):
        await playground_channel.on_any('unknown', 1, 2)

@pytest.mark.asyncio
async def test_playground_git(create_playground_channel, mocker):
    playground_channel = await create_playground_channel
    playground = playground_channel.current_playground

    def side_effect(cmd):
        if cmd.startswith('git rev-parse'):
            return 'origin/develop'
        else:
            return 'M  test.txt'

    mocker.patch.object(playground.ide_server_client, 'agent_terminal_with_result', side_effect=side_effect)
    mocker.patch.object(playground.agent_controller.git_role, 'aask', return_value='commit message here')
    res = await playground_channel.on_u_make_commit_file_list()
    assert len(res) == 1
    await playground_channel.on_u_make_commit_message()

    mocker.patch.object(
        playground.agent_controller.git_role, 'aask',
        return_value=PRMessageModel(
            suggested_title='title',
            suggested_description='description'
        )
    )
    await playground_channel.on_u_make_pr_file_list('origin/develop')
    await playground_channel.on_u_make_pr_message('origin/develop')

@pytest.mark.asyncio
async def test_playground_git_timeout(create_playground_channel, mocker):
    playground_channel = await create_playground_channel
    mocker.patch.object(
        playground_channel.current_playground.ide_server_client,
        'agent_terminal_with_result',
        side_effect=IDEServerFunCallTimeoutException('Failed to check cmd completion within the specified time')
    )
    workspace = playground_channel.current_playground.agent_controller.workspace
    mock_trigger = mocker.patch.object(workspace, 'trigger')
    with pytest.raises(AgentRunException, match='Git operation takes longer than expected.'):
        await playground_channel.on_u_make_commit_message()
        mock_trigger.assert_called_once()

@pytest.mark.asyncio
async def test_playground_git_null(create_playground_channel, mocker):
    playground_channel = await create_playground_channel
    playground = playground_channel.current_playground

    def side_effect(cmd):
        if cmd.startswith('git rev-parse'):
            return 'origin/develop'
        else:
            return ''

    mocker.patch.object(playground.ide_server_client, 'agent_terminal_with_result', side_effect=side_effect)
    res = await playground_channel.on_u_make_commit_file_list()
    assert res == []
    with pytest.raises(AgentRunException):
        await playground_channel.on_u_make_commit_message()

@pytest.mark.asyncio
async def test_playground_modify_step_or_action(create_playground_channel):
    playground_channel = await create_playground_channel
    task = create_test_task()
    playground_channel.current_playground.agent_controller.workspace.set_task(task)

    task_role = playground_channel.current_playground.agent_controller.task_role
    task_role.task_state.current_state = task_role.task_state.done

    # 测试创建step
    step_id = await playground_channel.on_u_add_step(
        {
            'title': 'title1',
            'task_actions': [
                {
                    'action': 'modify_file',
                    'action_object': {
                        'path': 'file1.txt',
                        'target': 'testClass',
                        'detailed_requirement': 'xxx'
                    }
                }
            ],
        }
    )
    assert step_id is not None

    await playground_channel.on_u_modify_step(step_id, 'title-modify')

    # 测试创建action
    action_id = await playground_channel.on_u_add_action(
        step_id,
        {
            'action': 'modify_file',
            'action_object': {
                'path': 'file2.txt',
                'target': 'testClass',
                'detailed_requirement': 'xxx'
            }
        },
    )
    assert action_id is not None
    task_action = (
        playground_channel.current_playground.agent_controller.task.find_action_by_id(
            action_id
        )
    )
    assert task_action.action_object.path == 'file2.txt'

    # 测试修改action
    await playground_channel.on_u_modify_action(
        action_id,
        {
            'action': 'modify_file',
            'action_object': {
                'path': 'file3.txt',
                'target': 'testClass',
                'detailed_requirement': 'xxx'
            }
        },
    )
    assert task_action.action_object.path == 'file3.txt'

    # 测试删除action
    await playground_channel.on_u_delete_action(action_id)
    with pytest.raises(AgentRunException):
        playground_channel.current_playground.agent_controller.task.find_action_by_id(
            action_id
        )

    # 测试删除step
    await playground_channel.on_u_delete_step(step_id)
    with pytest.raises(AgentRunException):
        playground_channel.current_playground.agent_controller.task.find_step_by_id(
            step_id
        )

@pytest.mark.asyncio
async def test_playground_rerun_task_action(mocker, create_playground_channel):
    playground_channel = await create_playground_channel
    task = create_test_task()
    controller = playground_channel.current_playground.agent_controller
    controller.workspace.set_task(task)
    task_role = controller.task_role
    task_role.task_state.current_state = task_role.task_state.done

    task_action = controller.task.find_action_by_id('1-2')
    task_action.action_object.snapshot_uuid = '1234'

    mocker.patch.object(
        playground_channel.current_playground.ide_server_client,
        'agent_query_snapshot_file',
        return_value=[{'uuid': '1234', 'value': {'content': 'test'}}]
    )
    terminal_mock = mocker.patch.object(
        playground_channel.current_playground.ide_server_client,
        'agent_terminal_with_result',
        return_value='ok'
    )
    original_aask = controller.code_role.aask
    async def mock_aask(*args, **kwargs):
        if kwargs.get('response_model') == CommandResultModel:
            return CommandResultModel(success=True, reason="Command executed successfully")
        if kwargs.get('response_model') == TerminalInteractResponseModel:
            return TerminalInteractResponseModel(needs_input=False, response='')
        return await original_aask(*args, **kwargs)
    mocker.patch.object(controller.code_role, 'aask', side_effect=mock_aask)

    mocker.patch.object(LLM, 'async_send', return_value=FileSnippet(content='edited content'))
    task_action.set_status_abandoned("test set to abandoned")
    await playground_channel.on_u_rerun_task_action('1-2')
    await controller._asyncio_task
    assert controller.task.find_action_by_id('1-2').status == 'completed'

    # 耗时action 重新执行时，timeout 会延长
    long_run_task_action = controller.task.find_action_by_id('2-3')
    long_run_task_action.set_status_abandoned("test set to abandoned")
    await playground_channel.on_u_rerun_task_action('2-3')
    await controller._asyncio_task
    assert controller.task.find_action_by_id('2-3').status == 'completed'
    assert terminal_mock.call_args_list[0].kwargs['hard_timeout'] == 0

@pytest.mark.asyncio
async def test_playground_toggle_revert_action(create_playground_channel):
    playground_channel = await create_playground_channel
    controller = playground_channel.current_playground.agent_controller

    with pytest.raises(UserTaskStatusException, match='Failed to toggle revert action, task not found'):
        await playground_channel.on_u_toggle_revert_action('1-1')

    task = create_test_task()
    controller.workspace.set_task(task)
    task_role = controller.task_role
    task_role.task_state.current_state = task_role.task_state.done

    # Test toggle with invalid task action id
    with pytest.raises(AgentRunException, match='The specified task_action_id could not be found: invalid-id'):
        await playground_channel.on_u_toggle_revert_action('invalid-id')

    task_action = controller.task.find_action_by_id('1-1')
    task_action.set_status_completed()

    # Test toggle from completed to canceled
    await playground_channel.on_u_toggle_revert_action('1-1')
    assert task_action.status == 'canceled'

    # Test toggle from canceled to completed
    await playground_channel.on_u_toggle_revert_action('1-1')
    assert task_action.status == 'completed'

    # Test toggle with invalid status
    task_action.set_status_doing()
    with pytest.raises(UserTaskStatusException, match='Failed to toggle revert action, current task action status is not allowed'):
        await playground_channel.on_u_toggle_revert_action('1-1')


@pytest.mark.asyncio
async def test_playground_cmd_k(mocker, create_playground_channel):
    playground_channel = await create_playground_channel
    mocker.patch.object(
        playground_channel.current_playground.ide_server_client,
        'agent_read_file',
        return_value="test"
    )

    async def side_effect(*args, **kwargs):
        callback = kwargs.get('chunk_callback')
        if callback:
            await callback('test')
            await callback('test2')
        return "test"

    mocker.patch.object(LLM, 'async_send', side_effect=side_effect)
    mocker.patch.object(playground_channel.logger, 'error')

    with pytest.raises(UserEventArgumentException, match="start_line and end_line must be positive integers"):
        await playground_channel.on_u_cmd_k('file1.txt', -10, -5, 'test')

    with pytest.raises(UserEventArgumentException, match="start_line must be less than or equal to end_line"):
        await playground_channel.on_u_cmd_k('file1.txt', 10, 5, 'test')

    await playground_channel.on_u_cmd_k('file1.txt', 1, 5, 'test')
    await playground_channel._asyncio_cmd_k
    await playground_channel.on_u_cmd_k('file1.txt', "1", "5", 'test')
    await playground_channel._asyncio_cmd_k

@pytest.mark.asyncio
async def test_playground_run_cmd(mocker, create_playground_channel):
    playground_channel = await create_playground_channel
    mocker.patch.object(
        playground_channel.current_playground.ide_server_client,
        'agent_terminal_with_result',
        return_value="ok"
    )

    res = await playground_channel.on_u_run_cmd('ls')
    assert res == 'ok'

@pytest.mark.asyncio
async def test_playground_expand_task_turn(mocker, create_playground_channel):
    playground_channel = await create_playground_channel
    task = create_test_task()
    controller = playground_channel.current_playground.agent_controller
    controller.workspace.set_task(task)
    task_role = controller.task_role
    task_role.task_state.current_state = task_role.task_state.done
    assert len(task.task_steps) == 2
    task_step_dict = {
        'title': 'test_title1',
        'task_actions': [
            {'action': 'modify_file', 'action_object': {'path': 'file1.txt', 'target': 'testClass', 'detailed_requirement': 'xxx'}}
        ],
    }
    task.task_steps[0].task_actions[0].set_status_completed()
    task.task_steps[1].task_actions[0].set_status_completed()
    task_role.task_state.current_state = task_role.task_state.done
    appended_limit = task_role.task_state.appended_limit
    for i in range(appended_limit):
        await playground_channel.on_u_add_step(task_step_dict)
        assert controller.task_state.appended_expansion_turn == 1
        assert task.task_steps[-1].turn == i+1
        await playground_channel.on_u_start_task()
        await playground_channel.current_playground.agent_controller._asyncio_task
        # Wait for summary task to complete
        if controller._asyncio_summary_role_task:
            await controller._asyncio_summary_role_task

    assert len(task.task_steps) == appended_limit+2
    with pytest.raises(UserTaskAppendLimitException):
        await playground_channel.on_u_add_step(task_step_dict)
    await playground_channel.on_u_expand_task_turn()
    assert controller.task_state.appended_expansion_turn == 2
    await playground_channel.on_u_add_step(task_step_dict)
    assert task.task_steps[-1].turn == appended_limit+1
    assert len(task.task_steps) == appended_limit+3

@pytest.mark.asyncio
async def test_playground_init_empty_task(mocker, create_playground_channel):
    playground_channel = await create_playground_channel
    await playground_channel.on_u_init_empty_task(title='test_title', description='test_desc')
    controller = playground_channel.current_playground.agent_controller
    assert controller.task
    assert controller.task.title == 'test_title'
    assert controller.task.description == 'test_desc'
    assert len(controller.task.task_steps) == 0

@pytest.mark.asyncio
async def test_playground_on_u_refine_prompt(mocker, create_playground_channel):
    playground_channel = await create_playground_channel
    controller = playground_channel.current_playground.agent_controller
    mocker.patch.object(
        controller.spec_role,
        'refine_prompt',
        return_value="refined prompt text"
    )
    result = await playground_channel.on_u_refine_prompt("original prompt", "chat")

    assert result == "refined prompt text"

@pytest.mark.asyncio
async def test_on_u_auto_fix_enable_true(mocker, create_playground_channel):
    playground_channel = await create_playground_channel
    controller = playground_channel.current_playground.agent_controller
    await playground_channel.on_u_auto_fix_enable(True)

    assert controller.workspace.auto_fix.enable is True
    await playground_channel.on_u_auto_fix_enable(False)
    assert controller.workspace.auto_fix.enable is False

@pytest.mark.asyncio
async def test_on_u_start_auto_fix(mocker, create_playground_channel):
    playground_channel = await create_playground_channel
    controller = playground_channel.current_playground.agent_controller

    # Mock the auto fix task
    mocker.patch.object(controller, 'start_auto_fix_task', return_value=asyncio.sleep(0.1))

    result = await playground_channel.on_u_start_auto_fix()
    assert result is True

    # Verify the auto fix task was added
    assert controller._asyncio_auto_fix_error_task is not None
