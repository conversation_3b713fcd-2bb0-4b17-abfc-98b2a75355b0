import pytest
import json
from heracles.core.exceptions import Agent<PERSON><PERSON>Ex<PERSON>
from heracles.core.schema.models import (EnvironmentAnalysisResultModel, EnvironmentMatch,
                                         MiddlewareMatch, ResultMessageModel, PlanDraftResultModel)
from heracles.agent_roles.analyze_role.utils import group_and_sort_environments
from heracles.core.schema.knowledge import (RepoBasicInfo, BasicInfoModel, LanguageType,
                                            FrameworkType, RuntimeType, ProjectKnowledge, DetailedInfo)
from heracles.core.utils.redis_cache import redis_cache
from typing import Any

@pytest.mark.asyncio
async def test_analyze_project_basic_info(create_workspace, mocker):
    workspace = await create_workspace

    # 清空Redis缓存，避免使用之前的缓存数据
    analyze_role = workspace.playground.agent_controller.analyze_role
    cache_key = analyze_role.get_project_knowledge_key()
    redis_cache.delete(cache_key)

    mocker.patch.object(analyze_role, 'aask', return_value=RepoBasicInfo(
        basic_info_list=[
            BasicInfoModel(
                app_dir=".",
                language=LanguageType.JAVASCRIPT,
                framework=FrameworkType.HTML_CSS_JS,
                framework_version="",
                runtime=RuntimeType.NA,
                runtime_version="",
                dependency_command="",
                run_command="browser-sync start --server --no-notify --no-open --files '**/*.css, **/*.html, **/*.js'",
                middlewares=["astra-db", "mongodb"],
            )
        ]
    ))
    await analyze_role.extract_basic_info()
    # test astra-db not in middlewares
    assert workspace.project_knowledge.middlewares == ["mongodb"]

def test_group_and_sort_environments():
    environment_list = [
        {
            "id": "403710023139721216",
            "name": "Dart",
            "runtime": "dart",
            "packageManagers": [],
            "language": "Dart"
        },
        {
            "id": "403710023139721217",
            "name": "Flutter+Dart",
            "runtime": "dart",
            "packageManagers": [],
            "language": "Dart"
        },
        {
            "id": "403710023303299073",
            "name": "Node.js 20",
            "runtime": "node 20",
            "packageManagers": [
                "npm 10.7.0",
                "pnpm 9.14.3",
                "yarn 1.22.22"
            ],
            "language": "Node.js"
        },
        {
            "id": "403710023303299074",
            "name": "Node.js 22",
            "runtime": "node 22",
            "packageManagers": [
                "npm 10.7.0",
                "pnpm 9.14.3",
                "yarn 1.22.22"
            ],
            "language": "Node.js"
        },
        {
            "id": "684533895370272770",
            "name": "Node.js 18",
            "runtime": "node 18",
            "packageManagers": [
                "npm 10.7.0",
                "pnpm 9.14.3",
                "yarn 1.22.22"
            ],
            "language": "Node.js"
        },
        {
            "id": "403710023303299075",
            "name": "Vue 3+Node.js 20",
            "runtime": "node 20",
            "packageManagers": [
                "npm 10.7.0",
                "pnpm 9.14.3",
                "yarn 1.22.22"
            ],
            "language": "Node.js"
        },
        {
            "id": "403710023303299076",
            "name": "React 15+Node.js 20",
            "runtime": "node 20",
            "packageManagers": [
                "npm 10.7.0",
                "pnpm 9.14.3",
                "yarn 1.22.22"
            ],
            "language": "Node.js"
        },
        {
            "id": "403710023303299077",
            "name": "Angular+Node.js 20",
            "runtime": "node 20",
            "packageManagers": [
                "npm 10.7.0",
                "pnpm 9.14.3",
                "yarn 1.22.22"
            ],
            "language": "Node.js"
        },
        {
            "id": "403710023303299078",
            "name": "Express+Node.js 20",
            "runtime": "node 20",
            "packageManagers": [
                "npm 10.7.0",
                "pnpm 9.14.3",
                "yarn 1.22.22"
            ],
            "language": "Node.js"
        },
        {
            "id": "403710023303299079",
            "name": "Next+Node.js 20",
            "runtime": "node 20",
            "score": 0.7,
            "packageManagers": [
                "npm 10.7.0",
                "pnpm 9.14.3",
                "yarn 1.22.22"
            ],
            "language": "Node.js"
        },
        {
            "id": "403710023458488320",
            "name": "Node.js 18",
            "runtime": "node 18",
            "score": 0.9,
            "packageManagers": [
                "npm 10.7.0",
                "pnpm 9.14.3",
                "yarn 1.22.22"
            ],
            "language": "HTML/CSS/JS"
        }
    ]

    result = group_and_sort_environments(environment_list)
    assert result[0]['name'] == 'HTML/CSS/JS'
    assert result[0]['score'] == 0.9
    assert result[0]['versionList'][0]['name'] == 'Node.js 18'
    assert result[0]['versionList'][0]['runtime'] == 'node 18'
    assert result[0]['versionList'][0]['score'] == 0.9
    assert result[1]['score'] == 0.7
    assert result[1]['versionList'][0]['name'] == 'Next+Node.js 20'
    assert result[1]['versionList'][0]['runtime'] == 'node 20'
    assert result[1]['versionList'][0]['score'] == 0.7

@pytest.mark.asyncio
async def test_analyze_project_environment_success(create_workspace, mocker):
    workspace = await create_workspace

    # 清空Redis缓存，避免使用之前的缓存数据
    analyze_role = workspace.playground.agent_controller.analyze_role
    cache_key = analyze_role.get_project_knowledge_key()
    redis_cache.delete(cache_key)

    env_response = {
        'success': True,
        'data': [
            {
                'id': '1',
                'name': 'Python 3.9',
                'runtime': 'python3.9',
                'language': 'Python',
                'packageManagers': ['pip'],
                'tags': ['web'],
                'runtimeInformation': {}
            }
        ]
    }
    middleware_response = {
        'success': True,
        'data': [
            {
                'id': '1',
                'name': 'FastAPI',
                'type': 'web',
                'language': 'Python',
                'tags': ['web']
            }
        ]
    }
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_environments', mocker.AsyncMock(return_value=env_response))
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_paas_middlewares', mocker.AsyncMock(return_value=middleware_response))

    analyze_role = workspace.playground.agent_controller.analyze_role
    mocker.patch.object(analyze_role, 'aask', return_value=EnvironmentAnalysisResultModel(
        message_type='info',
        reason='',
        instructions=[],
        environments=[EnvironmentMatch(
            id='1',
            score=1.0,
        )],
        middlewares=[MiddlewareMatch(
            id='1',
            score=1.0,
            reason='reason'
        )]
    ))
    result = await analyze_role.analyze_project_environment()
    assert result is not None

@pytest.mark.asyncio
async def test_analyze_project_environment_failed_environments(create_workspace, mocker):
    workspace = await create_workspace
    workspace.trigger = mocker.AsyncMock()

    env_response = {
        'success': False,
        'data': None,
        'error': 'Failed to fetch environments'
    }
    mock = mocker.patch(
        'heracles.agent_roles.analyze_role.utils.get_environments',
        mocker.AsyncMock(return_value=env_response)
    )

    role = workspace.playground.agent_controller.analyze_role
    with pytest.raises(AgentRunException, match='Failed to fetch environments'):
        await role.analyze_project_environment()
    mock.assert_called_once()

@pytest.mark.asyncio
async def test_analyze_project_environment_failed_middlewares(create_workspace, mocker):
    workspace = await create_workspace
    workspace.trigger = mocker.AsyncMock()

    env_response = {
        'success': True,
        'data': [
            {
                'id': '1',
                'name': 'Python 3.9',
                'runtime': 'python3.9',
                'language': 'Python',
                'packageManagers': ['pip'],
                'tags': ['web'],
                'runtimeInformation': {}
            }
        ]
    }
    middleware_response = {
        'success': False,
        'data': None,
        'error': 'Failed to fetch middlewares'
    }
    mock_get_environments = mocker.patch(
        'heracles.agent_roles.analyze_role.utils.get_environments',
        mocker.AsyncMock(return_value=env_response)
    )
    mock_get_middlewares = mocker.patch(
        'heracles.agent_roles.analyze_role.utils.get_paas_middlewares',
        mocker.AsyncMock(return_value=middleware_response)
    )
    mocker.patch.object(workspace, 'trigger')

    role = workspace.playground.agent_controller.analyze_role
    with pytest.raises(AgentRunException, match='Failed to fetch middlewares'):
        await role.analyze_project_environment()
    mock_get_environments.assert_called_once()
    mock_get_middlewares.assert_called_once()

@pytest.mark.asyncio
async def test_get_project_knowledge_key_with_project_id(create_workspace, mocker):
    workspace = await create_workspace
    workspace.get_project_id = mocker.Mock(return_value="test_project_123")

    analyze_role = workspace.playground.agent_controller.analyze_role
    key = analyze_role.get_project_knowledge_key()

    assert key == "project_knowledge:basic_info:test_project_123"


@pytest.mark.asyncio
async def test_get_project_knowledge_key_without_project_id(create_workspace, mocker):
    workspace = await create_workspace
    workspace.get_project_id = mocker.Mock(return_value=None)
    workspace.playground.playground_id = "playground_456"

    analyze_role = workspace.playground.agent_controller.analyze_role
    key = analyze_role.get_project_knowledge_key()

    assert key == "project_knowledge:playground:basic_info:playground_456"


@pytest.mark.asyncio
async def test_load_runtime_environment_success(create_workspace, mocker):
    workspace = await create_workspace

    # Mock environment data
    mock_environment: dict[str, Any] = {
        'runtime': 'python',
        'version': '3.9',
        'os': 'linux'
    }

    # Mock middlewares
    mock_middlewares = ['redis', 'postgres']

    workspace.playground.ide_server_client.environment = mock_environment
    mocker.patch.object(workspace.tools, 'list_middlewares', return_value=mock_middlewares)

    analyze_role = workspace.playground.agent_controller.analyze_role
    result = await analyze_role.load_runtime_environment()

    expected = mock_environment.copy()
    expected['middlewares'] = mock_middlewares
    assert result == expected


@pytest.mark.asyncio
async def test_load_runtime_environment_no_environment(create_workspace):
    workspace = await create_workspace
    workspace.playground.ide_server_client.environment = None

    analyze_role = workspace.playground.agent_controller.analyze_role
    result = await analyze_role.load_runtime_environment()

    assert result is None


@pytest.mark.asyncio
async def test_load_project_knowledge_from_basic_info_with_runtime_environment(create_workspace, mocker):
    workspace = await create_workspace

    # Mock runtime environment
    mock_runtime_env = {
        'runtime': 'python',
        'version': '3.9',
        'middlewares': ['redis']
    }

    # Create test data
    repo_basic_info = RepoBasicInfo(
        basic_info_list=[
            BasicInfoModel(
                app_dir=".",
                language=LanguageType.PYTHON,
                framework=FrameworkType.FASTAPI,
                framework_version="",
                runtime=RuntimeType.CPYTHON,
                runtime_version="3.9",
                dependency_command="pip install -r requirements.txt",
                run_command="uvicorn app:app",
                middlewares=["redis", "mysql", "invalid_middleware"],
            )
        ]
    )

    analyze_role = workspace.playground.agent_controller.analyze_role
    mocker.patch.object(analyze_role, 'load_runtime_environment', return_value=mock_runtime_env)

    result = await analyze_role.load_project_knowledge_from_basic_info(repo_basic_info)

    assert result.basic_info_list[0].middlewares == ["redis", "mysql"]


@pytest.mark.asyncio
async def test_load_project_knowledge_from_basic_info_without_runtime_environment(create_workspace, mocker):
    workspace = await create_workspace

    # Create test data
    repo_basic_info = RepoBasicInfo(
        basic_info_list=[
            BasicInfoModel(
                app_dir=".",
                language=LanguageType.JAVASCRIPT,
                framework=FrameworkType.REACT,
                framework_version="18.0.0",
                runtime=RuntimeType.NODEJS,
                runtime_version="18.0.0",
                dependency_command="npm install",
                run_command="npm start",
                middlewares=["mongodb", "postgres", "invalid_middleware"],
            )
        ]
    )

    analyze_role = workspace.playground.agent_controller.analyze_role
    mocker.patch.object(analyze_role, 'load_runtime_environment', return_value=None)

    result = await analyze_role.load_project_knowledge_from_basic_info(repo_basic_info)

    assert result.runtime_environment == {}  # Default empty dict when no runtime environment
    # Test middleware filtering
    assert result.basic_info_list[0].middlewares == ["mongodb", "postgres"]


@pytest.mark.asyncio
async def test_load_project_knowledge_from_cache_with_runtime_environment(create_workspace, mocker):
    workspace = await create_workspace

    mock_runtime_env = {
        'runtime': 'node',
        'version': '18',
        'middlewares': ['redis']
    }

    # Mock cached data
    cached_data = {
        'basic_info_list': [{
            'app_dir': '.',
            'language': 'javascript',
            'framework': 'react',
            'framework_version': '18.0.0',
            'runtime': 'nodejs',
            'runtime_version': '18.0.0',
            'dependency_command': 'npm install',
            'run_command': 'npm start',
            'middlewares': ['redis'],
            'project_dependencies': '',
            'project_components': ''
        }],
        'project_structure': '',
        'runtime_environment': {}
    }

    analyze_role = workspace.playground.agent_controller.analyze_role

    mocker.patch.object(redis_cache, 'get', return_value=json.dumps(cached_data))
    mocker.patch.object(analyze_role, 'load_runtime_environment', return_value=mock_runtime_env)

    result = await analyze_role.load_project_knowledge()

    assert result is not None
    assert result.runtime_environment == mock_runtime_env


@pytest.mark.asyncio
async def test_load_project_knowledge_aask_failure_not_mock_data(create_workspace, mocker):
    workspace = await create_workspace

    analyze_role = workspace.playground.agent_controller.analyze_role
    cache_key = analyze_role.get_project_knowledge_key()

    # Clear cache
    redis_cache.delete(cache_key)

    # Mock aask to return non-RepoBasicInfo result
    mocker.patch.object(analyze_role, 'aask', return_value="Some error message")

    # Mock logger to capture warning
    mock_logger = mocker.patch.object(analyze_role, 'logger')

    result = await analyze_role.load_project_knowledge()

    assert result is None
    mock_logger.warning.assert_called_once_with("extract project 'project basic info' failed")


@pytest.mark.asyncio
async def test_load_project_knowledge_aask_failure_mock_data(create_workspace, mocker):
    workspace = await create_workspace

    analyze_role = workspace.playground.agent_controller.analyze_role
    cache_key = analyze_role.get_project_knowledge_key()

    # Clear cache
    redis_cache.delete(cache_key)

    # Mock aask to return mock data
    mocker.patch.object(analyze_role, 'aask', return_value="LLM mock data")

    # Mock logger to capture warning
    mock_logger = mocker.patch.object(analyze_role, 'logger')

    result = await analyze_role.load_project_knowledge()

    assert result is None
    # Should not log warning for mock data
    mock_logger.warning.assert_not_called()


@pytest.mark.asyncio
async def test_extract_basic_info_success(create_workspace, mocker):
    workspace = await create_workspace

    mock_project_knowledge = ProjectKnowledge(
        basic_info_list=[
            DetailedInfo(
                app_dir=".",
                language=LanguageType.PYTHON,
                framework=FrameworkType.FASTAPI,
                framework_version="",
                runtime=RuntimeType.CPYTHON,
                runtime_version="3.9",
                dependency_command="pip install -r requirements.txt",
                run_command="uvicorn app:app",
                middlewares=["redis"],
            )
        ]
    )

    analyze_role = workspace.playground.agent_controller.analyze_role
    mocker.patch.object(analyze_role, 'load_project_knowledge', return_value=mock_project_knowledge)

    await analyze_role.extract_basic_info()

    assert workspace.project_knowledge == mock_project_knowledge
    # Check that the analyze item was set to done
    basic_info_item = [item for item in workspace.analyze_items if item.name == "project_basic_info"][0]
    assert basic_info_item.status == "done"


@pytest.mark.asyncio
async def test_extract_basic_info_failure(create_workspace, mocker):
    workspace = await create_workspace

    analyze_role = workspace.playground.agent_controller.analyze_role
    mocker.patch.object(analyze_role, 'load_project_knowledge', return_value=None)

    await analyze_role.extract_basic_info()

    # Check that the analyze item was set to failed
    basic_info_item = [item for item in workspace.analyze_items if item.name == "project_basic_info"][0]
    assert basic_info_item.status == "failed"


@pytest.mark.asyncio
async def test_run_with_specific_analyze_item_project_structure(create_workspace, mocker):
    workspace = await create_workspace

    # Set up mock project knowledge
    workspace.project_knowledge = ProjectKnowledge(
        basic_info_list=[
            DetailedInfo(
                app_dir=".",
                language=LanguageType.PYTHON,
                framework=FrameworkType.FASTAPI,
                framework_version="",
                runtime=RuntimeType.CPYTHON,
                runtime_version="3.9",
                dependency_command="pip install -r requirements.txt",
                run_command="uvicorn app:app",
                middlewares=["redis"],
            )
        ]
    )

    analyze_role = workspace.playground.agent_controller.analyze_role
    mock_process = mocker.patch.object(analyze_role, '_process_analyze_item')

    result = await analyze_role.run('project_structure')

    # Should call _process_analyze_item with workspace.project_knowledge
    mock_process.assert_called_once()
    args = mock_process.call_args[0]
    assert args[0] == workspace.project_knowledge
    assert args[1].name == 'project_structure'
    assert result == workspace.detail_status


@pytest.mark.asyncio
async def test_run_with_specific_analyze_item_not_project_structure(create_workspace, mocker):
    workspace = await create_workspace

    # Set up mock project knowledge
    basic_info = DetailedInfo(
        app_dir=".",
        language=LanguageType.PYTHON,
        framework=FrameworkType.FASTAPI,
        framework_version="",
        runtime=RuntimeType.CPYTHON,
        runtime_version="3.9",
        dependency_command="pip install -r requirements.txt",
        run_command="uvicorn app:app",
        middlewares=["redis"],
    )

    workspace.project_knowledge = ProjectKnowledge(basic_info_list=[basic_info])

    analyze_role = workspace.playground.agent_controller.analyze_role
    mock_process = mocker.patch.object(analyze_role, '_process_analyze_item')

    result = await analyze_role.run('project_dependencies')

    # Should call _process_analyze_item with each basic_info
    assert mock_process.call_count == 1
    args = mock_process.call_args[0]
    assert args[0] == basic_info
    assert args[1].name == 'project_dependencies'
    assert result == workspace.detail_status


@pytest.mark.asyncio
async def test_process_analyze_item_success(create_workspace, mocker):
    workspace = await create_workspace

    # Create a mock analyze item
    analyze_item = mocker.MagicMock()
    analyze_item.instruction_prompt = "Test instruction"
    analyze_item.name = "test_item"
    analyze_item.set_running = mocker.AsyncMock()
    analyze_item.set_done = mocker.AsyncMock()
    analyze_item.set_failed = mocker.AsyncMock()

    # Create a mock target object
    target_object = mocker.MagicMock()

    # Mock successful result
    success_result = ResultMessageModel(success=True, result="Test result")

    analyze_role = workspace.playground.agent_controller.analyze_role
    mocker.patch.object(analyze_role, 'aask', return_value=success_result)

    # Mock PromptBuilder to avoid template formatting issues
    mock_prompt_builder = mocker.patch('heracles.agent_roles.analyze_role.PromptBuilder')
    mock_prompt_instance = mocker.MagicMock()
    mock_prompt_instance.format = mocker.AsyncMock(return_value="Formatted prompt")
    mock_prompt_builder.return_value = mock_prompt_instance

    await analyze_role._process_analyze_item(target_object, analyze_item)

    analyze_item.set_running.assert_called_once()
    analyze_item.set_done.assert_called_once()
    analyze_item.set_failed.assert_not_called()

    # Check that the attribute was set on target_object
    assert hasattr(target_object, analyze_item.name)


@pytest.mark.asyncio
async def test_process_analyze_item_failure_result_message_model(create_workspace, mocker):
    workspace = await create_workspace

    # Create a mock analyze item
    analyze_item = mocker.MagicMock()
    analyze_item.instruction_prompt = "Test instruction"
    analyze_item.name = "test_item"
    analyze_item.set_running = mocker.AsyncMock()
    analyze_item.set_done = mocker.AsyncMock()
    analyze_item.set_failed = mocker.AsyncMock()

    # Create a mock target object
    target_object = mocker.MagicMock()

    # Mock failed result
    failed_result = ResultMessageModel(success=False, result="Error message")

    analyze_role = workspace.playground.agent_controller.analyze_role
    mocker.patch.object(analyze_role, 'aask', return_value=failed_result)

    # Mock PromptBuilder to avoid template formatting issues
    mock_prompt_builder = mocker.patch('heracles.agent_roles.analyze_role.PromptBuilder')
    mock_prompt_instance = mocker.MagicMock()
    mock_prompt_instance.format = mocker.AsyncMock(return_value="Formatted prompt")
    mock_prompt_builder.return_value = mock_prompt_instance

    # Mock logger
    mock_logger = mocker.patch.object(analyze_role, 'logger')

    await analyze_role._process_analyze_item(target_object, analyze_item)

    analyze_item.set_running.assert_called_once()
    analyze_item.set_done.assert_not_called()
    analyze_item.set_failed.assert_called_once()

    mock_logger.error.assert_called_once()
    assert "extract project knowledge 'test_item' failed, reason: Error message" in mock_logger.error.call_args[0][0]


@pytest.mark.asyncio
async def test_process_analyze_item_failure_not_result_message_model(create_workspace, mocker):
    workspace = await create_workspace

    # Create a mock analyze item
    analyze_item = mocker.MagicMock()
    analyze_item.instruction_prompt = "Test instruction"
    analyze_item.name = "test_item"
    analyze_item.set_running = mocker.AsyncMock()
    analyze_item.set_done = mocker.AsyncMock()
    analyze_item.set_failed = mocker.AsyncMock()

    # Create a mock target object
    target_object = mocker.MagicMock()

    analyze_role = workspace.playground.agent_controller.analyze_role
    mocker.patch.object(analyze_role, 'aask', return_value="Some invalid result")

    # Mock PromptBuilder to avoid template formatting issues
    mock_prompt_builder = mocker.patch('heracles.agent_roles.analyze_role.PromptBuilder')
    mock_prompt_instance = mocker.MagicMock()
    mock_prompt_instance.format = mocker.AsyncMock(return_value="Formatted prompt")
    mock_prompt_builder.return_value = mock_prompt_instance

    # Mock logger
    mock_logger = mocker.patch.object(analyze_role, 'logger')

    await analyze_role._process_analyze_item(target_object, analyze_item)

    analyze_item.set_running.assert_called_once()
    analyze_item.set_done.assert_not_called()
    analyze_item.set_failed.assert_called_once()

    mock_logger.error.assert_called_once()
    assert "extract project knowledge 'test_item' failed, extract result: Some invalid result" in mock_logger.error.call_args[0][0]


@pytest.mark.asyncio
async def test_process_analyze_item_no_instruction_prompt(create_workspace, mocker):
    workspace = await create_workspace

    # Create a mock analyze item without instruction prompt
    analyze_item = mocker.MagicMock()
    analyze_item.instruction_prompt = None
    analyze_item.name = "test_item"

    # Create a mock target object
    target_object = mocker.MagicMock()

    analyze_role = workspace.playground.agent_controller.analyze_role

    # Mock logger
    mock_logger = mocker.patch.object(analyze_role, 'logger')

    await analyze_role._process_analyze_item(target_object, analyze_item)

    mock_logger.warning.assert_called_once()
    assert "analyze item" in mock_logger.warning.call_args[0][0]
    assert "has no instruction prompt, skipped" in mock_logger.warning.call_args[0][0]


@pytest.mark.asyncio
async def test_create_project_draft(create_workspace, mocker):
    workspace = await create_workspace

    # Mock dependencies
    env_list = [{'id': '1', 'name': 'Python 3.9'}]
    middleware_list = [{'id': '1', 'name': 'Redis'}]

    mocker.patch(
        'heracles.agent_roles.analyze_role.get_available_environments_and_middlewares',
        return_value=(env_list, middleware_list)
    )

    # Mock extract_user_message
    mock_extract = mocker.patch(
        'heracles.agent_roles.analyze_role.extract_user_message',
        return_value=("Clean user message", {'images': [], 'webpages': []})
    )

    # Mock PlanDraftResultModel result
    draft_result = PlanDraftResultModel(
        project_name="test-project",
        message_type="info",
        reason="Test reason",
        instructions=["Step 1", "Step 2"],
        environments=[EnvironmentMatch(id="1", score=0.9)],
        middlewares=[MiddlewareMatch(id="1", score=0.8, reason="Good fit")]
    )

    analyze_role = workspace.playground.agent_controller.analyze_role
    mocker.patch.object(analyze_role, 'aask', return_value=draft_result)

    # Mock utility functions
    mocker.patch('heracles.agent_roles.analyze_role.merge_ai_result_to_environment_list')
    mocker.patch('heracles.agent_roles.analyze_role.merge_ai_result_to_middleware_list')
    mocker.patch('heracles.agent_roles.analyze_role.group_and_sort_environments', return_value=env_list)

    result = await analyze_role.create_project_draft("Create a Python web app")

    assert result['project_name'] == "test-project"
    assert result['message_type'] == "info"
    assert result['reason'] == "Test reason"
    assert result['instructions'] == ["Step 1", "Step 2"]
    assert result['environments'] == env_list
    assert result['middlewares'] == middleware_list

    mock_extract.assert_called_once_with("Create a Python web app", workspace)
