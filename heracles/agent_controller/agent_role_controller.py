import asyncio
import time
import uuid
import json
import traceback
from tenacity import retry, wait_exponential
from typing import Awaitable, Any

from heracles.core.utils import add_asyncio_handler, cancel_asyncio_handler, wait_for
from heracles.core.exceptions import Agent<PERSON>unException
from heracles.core.logger import get_playground_logger
from heracles.core.schema.task import Task
from heracles.core.schema import ErrorHandlerMessageType, ErrorHandlerMessage
from heracles.core.utils.crypt_utils import payment_auth_key, async_encrypt_content, async_decrypt_content

from heracles.agent_workspace.agent_workspace import AgentWorkspace
from heracles.agent_roles.analyze_role import AnalyzeRole
from heracles.agent_roles.chat_role import Chat<PERSON>ole
from heracles.agent_roles.cmd_k_role import CmdK<PERSON>ole
from heracles.agent_roles.code_role import CodeRole
from heracles.agent_roles.plan_role import PlanRole
from heracles.agent_roles.role_action import (
    AgentRoleCreateSpecAction,
    AgentRoleCreateStepAction,
    Agent<PERSON>oleNullAction
)
from heracles.agent_roles.spec_role import Spec<PERSON><PERSON>
from heracles.agent_roles.task_role import TaskRole
from heracles.agent_roles.summary_role import Summary<PERSON><PERSON>
from heracles.agent_roles.check_role import Check<PERSON>ole
from heracles.agent_roles.git_role import GitRole
from heracles.agent_roles.analyze_role.utils import find_best_match_env, find_match_middlewares

from .agent_role_controller_mixins import RoleUserEventsMixin, RoleGitEventsMixin


class AgentRoleController(RoleGitEventsMixin, RoleUserEventsMixin):
    def __init__(self, playground):
        self.playground = playground
        self.workspace = AgentWorkspace(self.playground)
        self.analyze_role = AnalyzeRole(self.workspace)
        self.chat_role = ChatRole(self.workspace)
        self.spec_role = SpecRole(self.workspace)
        self.plan_role = PlanRole(self.workspace)
        self.code_role = CodeRole(self.workspace)
        self.task_role = TaskRole(self.workspace)
        self.cmd_k_role = CmdKRole(self.workspace)
        self.summary_role = SummaryRole(self.workspace)
        self.check_role = CheckRole(self.workspace)
        self.git_role = GitRole(self.workspace)
        self.waiting_user_input = True

        # 为了持有后台异步任务的引用, 如 plan_task, run 等
        self._asyncio_analyze_role_task = None
        self._asyncio_task = None
        self._asyncio_message = None
        self._asyncio_summary_role_task = None
        self._asyncio_auto_fix_error_task = None

        self.logger = get_playground_logger(self.playground.playground_id)
        self.run_build_workspace_with_background()

    @property
    def task(self) -> Task | None:
        return self.workspace.task

    def reset_task(self):
        """ 清理 task
        """
        if self.task:
            self.task.delete()
            self.task_state.delete()
        self.task_role.reset_all()
        self._cancel_asyncio_task()

    @property
    def task_state(self):
        return self.task_role.task_state

    @task_state.setter
    def task_state(self, state):
        """设定任务状态"""
        self.task_role.task_state.set_state(state)

    async def make_spec(self, goal, goal_detail, from_chat=False):
        """澄清需求"""
        spec = await self.spec_role.run(goal, goal_detail)
        res = spec.dict()
        if payment_auth_key:
            encrypt_data: dict[str, Any] = {}
            encrypt_data['task_scale'] = spec.task_scale
            encrypt_data['spec_id'] = str(uuid.uuid4())
            encrypt_data['timestamp'] = int(time.time()*1000) # 取毫秒级时间戳
            sign_data = await async_encrypt_content(json.dumps(encrypt_data, sort_keys=True), payment_auth_key)
            res['sign_data'] = sign_data
        await self.workspace.trigger("spec_updated", res)
        if from_chat:
            success_message = (
                "Specification generated successfully as follows (this does not mean it is accepted and ran by the user):\n"
                f"{spec.model_dump_json()}"
            )
            self.chat_role.memory.transform_last_message_with_result(success_message)

    async def make_project_draft(self, goal, goal_detail) -> dict:
        """创建项目草稿: 环境信息+spec"""
        project_draft = await self.analyze_role.create_project_draft(f"GOAL: {goal}\n\nGOAL DETAIL: {goal_detail}")
        await self.workspace.trigger('project_draft_updated', project_draft)

        env = find_best_match_env(project_draft.get("environments", {}))
        middlewares = find_match_middlewares(project_draft.get("middlewares", []))
        env_description = f"This is a new project and it should be created in {env['name']} environment"
        if middlewares:
            env_description = env_description + f" with {', '.join([m['name'] for m in middlewares])}"

        self.logger.info(f"Create new project with env: {env_description}")
        goal_detail_with_env = goal_detail + "\n\n" + env_description
        return {
            'goal': goal,
            'goal_detail': goal_detail_with_env
        }

    async def make_plan(self, goal, goal_detail, proposed_list):
        """制定计划"""
        task = await self.plan_role.run(goal, goal_detail, proposed_list)
        self.workspace.set_task(task)

    async def init_empty_task(self, title: str, description: str = ''):
        """创建空任务"""
        task = Task(title=title, description=description)
        self.workspace.set_task(task)
        await self.workspace.trigger('task_planned', task.dict())

    async def make_additional_step(self, append_step_intent):
        """制定计划"""
        res = await self.plan_role.make_additional_step(append_step_intent)
        if not isinstance(res, AgentRoleNullAction):
            success_message = (
                "Step generated successfully as follows (this does not mean it is accepted and ran by the user):\n"
                f"{res.model_dump_json()}"
            )
            if self.chat_role.memory.count() > 0:
                self.chat_role.memory.transform_last_message_with_result(success_message)

            if not self._asyncio_auto_fix_error_task:
                await self.workspace.trigger('add_task_step_confirmation', res.to_dict())
            else:
                if self.task:
                    self.logger.warning(f"AddStep: task={self.task.title}, step={res.to_dict()}")
                    await self.workspace.trigger('auto_fix_add_task_step', res.to_dict())
                    self.task.add_task_step_from_dict(res.to_dict())
                    await self.task_role.trigger_task_updated(self.task)
                    self.task_state.set_state(self.task_state.working)
                    await self.task_role.run(self.code_role.run, delay_time=0)

    def run_build_workspace_with_background(self):
        """每次初始化后自动解析 basic_info 信息"""
        self._add_asyncio_analyze_role_task(self.init_workspace())

    @retry(wait=wait_exponential(multiplier=1, min=4, max=15))
    async def init_workspace(self):
        """完善构建 workspace"""
        # 监听 terminal 事件，持续收集 errors
        if self.workspace.auto_fix.enable:
            self.logger.warning('-> start monitoring errors')
            await self.workspace.smart_detect.set_status('monitoring_errors')
        self.workspace.observations.on_terminal(self.check_role.on_terminal)

        await self.workspace.playbook_manager.load_from_files()
        await self.playground.wait_for_ok(reason='build_workspace')
        await self.analyze_role.run('project_basic_info')

    async def build_workspace(self, analyze_item_name):
        await self.analyze_role.run(analyze_item_name)

    async def run_task(self, delay_time):
        """执行计划"""
        if not self.task:
            return
        self.task.run_turn += 1
        self.task.enable_autosave(self.playground.playground_id)

        await self.task_role.run(self.code_role.run, delay_time)
        if self.workspace.auto_fix.enable:
            self._add_asyncio_auto_fix_error_task(self.start_auto_fix_task())

    async def start_auto_fix_task(self):
        await self.workspace.auto_fix.update_status_and_event('start')
        if not self.task:
            await self.init_empty_task('AutoFix Test')
        while self.workspace.auto_fix.fix_turn < self.workspace.auto_fix.max_fix_turn:
            self.logger.info(f"AutoFix: start fix_turn={self.workspace.auto_fix.fix_turn}")
            try:
                await self.workspace.auto_fix.update_status_and_event('check_errors')
                await self.check_and_fix_errors()
                if self.workspace.auto_fix.status == 'fix_completed':
                    break
            except Exception as e:
                self.logger.error(f"Error in start_auto_fix_task: {e}")
                traceback.print_exc()
                await self.workspace.auto_fix.update_status_and_event('fix_stopped', str(e))
                break
            self.workspace.auto_fix.fix_turn += 1
        if not self.workspace.auto_fix.status == 'fix_completed':
            # 最大轮次后，修复依旧失败的情况
            await self.workspace.auto_fix.update_status_and_event('fix_stopped')

    async def check_and_fix_errors(self):
        """ 1 检查收集到的错误
            2 修复错误
            3 等待修复完成
        """
        self.workspace.smart_detect.clear_errors()
        await self.workspace.trigger_general_loading(tool_name='AutoFix', status='Wait for logs')
        await self.check_role.run_and_check_project()
        try:
            await wait_for(lambda: self.workspace.smart_detect.errors, timeout=10, interval=1)
        except AgentRunException:
            # 等待 10 秒后，仍旧没有错误，则检查截图
            await self.workspace.trigger_general_loading(tool_name='AutoFix', status='Checking browser screen')
            await self.check_role.analyze_screenshot()

            if not self.workspace.smart_detect.errors:
                await self.workspace.auto_fix.update_status_and_event('fix_completed')
                return
        # 分析处理发现的所有错误
        await self.workspace.trigger_general_loading(tool_name='AutoFix', status='Analyzing errors')
        await self.workspace.auto_fix.trigger_auto_fix_errors_updated()
        await self.workspace.auto_fix.update_status_and_event('fix_errors')
        await self.make_additional_step(
            {
                'goal': 'Fix errors cause project not running and any UI styling problems',
                'plan_draft': '1. Determine the issue severity 2. Find the root cause of the issue 3. Provide a fix for the issue',
                'references': ['error://' + str(error.ref_id) for error in self.workspace.smart_detect.get_errors()] +
                    ['image://' + str(image_url) for image_url in self.workspace.smart_detect.get_screenshot_error_urls()],
            }
        )

    def run_task_with_background(self):
        """启动背景任务来执行计划"""
        self._add_asyncio_task(self.run_task(delay_time=3), ErrorHandlerMessageType.TASK)

    async def rerun_task_action(self, task_action, delay_time=3):
        """执行单个计划"""
        self.task_state.rerun_task_action()
        await self.task_role.run_action(self.code_role.run, task_action, is_rerun=True)
        await asyncio.sleep(delay_time)
        self.task_state.end_task()

    async def run_message(self, message: str, memory_regenerate: bool, need_consider_task: bool):
        """处理消息"""
        role_action = await self.chat_role.run(
            message,
            memory_regenerate=memory_regenerate,
            need_consider_task=need_consider_task
        )

        if isinstance(role_action, AgentRoleCreateSpecAction):
            goal = role_action.goal
            goal_detail = role_action.goal_detail
            if self.workspace.get_project_id() is None:
                project_draft = await self.make_project_draft(goal, goal_detail)
                goal = project_draft['goal']
                goal_detail = project_draft['goal_detail']
            self.logger.info(f"CreateSpec: goal={goal}, goal_detail={goal_detail}")
            self._add_asyncio_task(
                self.make_spec(goal, goal_detail=goal_detail, from_chat=True),
                ErrorHandlerMessageType.SPEC,
            )
        elif isinstance(role_action, AgentRoleCreateStepAction):
            self.logger.info(
                f"CreateStep from chat_role: goal={role_action.goal}, plan_draft={role_action.plan_draft}, references={role_action.references}" # noqa: E501
            )
            append_step_intent = {
                'goal': role_action.goal,
                'plan_draft': role_action.plan_draft,
                'references': role_action.references,
            }
            self.logger.info(f"CreateStep: append_step_intent={append_step_intent}")
            await self.make_additional_step(append_step_intent)
        elif isinstance(role_action, AgentRoleNullAction):
            return
        else:
            self.logger.warning(f"RunMessage: got an unknown action {role_action}")
            raise AgentRunException(f"RunMessage: got an unknown action {role_action}")

    def _add_asyncio_task(self, future_task: Awaitable, error_type: ErrorHandlerMessageType):
        add_asyncio_handler(self, '_asyncio_task', future_task, self.trigger_error_handler, error_type, self.logger)

    def _has_another_task_running(self):
        if getattr(self, '_asyncio_task', None):
            return True
        return False

    def _add_asyncio_analyze_role_task(self, future_task):
        add_asyncio_handler(
            self,
            '_asyncio_analyze_role_task',
            future_task,
            self.trigger_error_handler,
            ErrorHandlerMessageType.ANALYZE,
            self.logger,
        )

    def _add_asyncio_summary_role_task(self, future_task):
        add_asyncio_handler(
            self,
            '_asyncio_summary_role_task',
            future_task,
            self.trigger_error_handler,
            ErrorHandlerMessageType.DEFAULT,
            self.logger,
        )

    def _add_asyncio_auto_fix_error_task(self, future_task):
        if self._asyncio_auto_fix_error_task:
            return
        add_asyncio_handler(
            self,
            '_asyncio_auto_fix_error_task',
            future_task,
            self.trigger_error_handler,
            ErrorHandlerMessageType.DEFAULT,
            self.logger,
        )

    def _cancel_asyncio_task(self):
        cancel_asyncio_handler(self, '_asyncio_task')

    def _cancel_asyncio_analyze_role_task(self):
        cancel_asyncio_handler(self, '_asyncio_analyze_role_task')

    def _cancel_asyncio_summary_role_task(self):
        cancel_asyncio_handler(self, '_asyncio_summary_role_task')

    def _cancel_asyncio_auto_fix_error_task(self):
        cancel_asyncio_handler(self, '_asyncio_auto_fix_error_task')

    def _cancel_asyncio_message(self):
        cancel_asyncio_handler(self, '_asyncio_message')

    async def trigger_error_handler(self, error_message: ErrorHandlerMessage):
        await self.playground.trigger_error_handler(error_message)

    async def wait_tasks(self):
        tasks = []
        if task := self._asyncio_task:
            tasks.append(task)
        if task := self._asyncio_message:
            tasks.append(task)
        if task := self._asyncio_analyze_role_task:
            tasks.append(task)
        if task := self._asyncio_summary_role_task:
            tasks.append(task)
        if task := self._asyncio_auto_fix_error_task:
            tasks.append(task)
        await asyncio.gather(*tasks)

    def stop_tasks(self):
        """ 清理任务 """
        self._cancel_asyncio_task()
        self._cancel_asyncio_message()
        self._cancel_asyncio_analyze_role_task()
        self._cancel_asyncio_summary_role_task()
        self._cancel_asyncio_auto_fix_error_task()

    async def expand_task_turn(self, sign_data=None):
        if sign_data and payment_auth_key:
            try:
                decrypted = await async_decrypt_content(sign_data, payment_auth_key)
                json_dict = json.loads(decrypted)
                expiration = 10000 # 10秒过期时间, 单位: 毫秒
                current_time = int(time.time()*1000) # 单位: 毫秒
                assert current_time < int(json_dict['timestamp']) + expiration
            except Exception as e:
                raise AgentRunException('Data integrity check failed in expand_task_turn') from e
        if not self.task:
            return
        self.task_state.appended_count = 0
        self.task_state.appended_expansion_turn += 1
        return self.task_state.appended_expansion_turn
