import asyncio
import json
from heracles.core.exceptions import Agent<PERSON><PERSON><PERSON>x<PERSON>
from heracles.core.schema import LLMAbilityType
from heracles.core.schema.models.task_model import TaskModel, TaskStepModel
from heracles.core.schema.plan import Plan, PlanStepType, ActionStatus, ThinkResult
from heracles.core.schema.task import Task
from heracles.core.utils.context_prompt_builder import <PERSON><PERSON><PERSON><PERSON><PERSON>
from heracles.agent_roles.utils import async_sanitize_generated_json
from heracles.agent_roles.role_base import RoleBase
from heracles.agent_roles.role_action import AgentRoleNullAction
from heracles.agent_roles.utils import with_general_loading, extract_referenced_contents
from pydantic import ValidationError
from .prompts import (
    THINK_SYSTEM_PROMPT,
    THINK_USER_PROMPT,
    GENERATE_SYSTEM_PROMPT,
    GENERATE_USER_PROMPT,
    ADDITIONAL_STEP_USER_PROMPT,
    ADDITIONAL_STEP_SYSTEM_PROMPT,
)


class PlanRole(RoleBase):
    """制定计划"""

    def __init__(self, workspace):
        super().__init__(workspace)
        self.plan = Plan()
        self.partial_task_str = ''
        self.partial_think_str = ''

    def get_llm_ability(self):
        return LLMAbilityType.STRONG

    async def pre_process_when_start_from_zero(self):
        file_tree = await self.workspace.tools.file_tree()
        if not (set(file_tree) - {".1024", ".gitignore", "README.md"}):
            for fp in set(file_tree):
                await self.workspace.playground.ide_server_client.agent_delete_file(fp)

    async def run(self, goal, goal_detail, proposed_list=None):
        # FIXME: 临时方案,从0-1时先删除目录所有文件避免影响框架初始化
        await self.pre_process_when_start_from_zero()  # noqa
        if proposed_list is None:
            proposed_list = []
        self.plan = Plan(goal=goal, goal_detail=goal_detail, proposed_list=proposed_list)

        # 先发一次 trigger 确保前端先正确初始好 plan
        await self.trigger_plan_updated(self.plan.dict())
        task = await self.make_plan(goal, goal_detail, proposed_list)
        await self.trigger_task_planned(task.dict())
        return task

    async def make_plan(self, goal, goal_detail, proposed_list):
        self.partial_think_str = ''
        self.partial_task_str = ''
        # RETRIEVE
        await self.update_plan_step(PlanStepType.RETRIEVE, {'status': ActionStatus.IN_PROGRESS_STATUS})
        related_snippets = await self._retrieve_related_code(goal, goal_detail, proposed_list)
        await self.update_plan_step(PlanStepType.RETRIEVE, {'status': ActionStatus.COMPLETED_STATUS, 'result': related_snippets})

        # THINK
        await self.update_plan_step(PlanStepType.THINK, {'status': ActionStatus.IN_PROGRESS_STATUS})
        await self.workspace.wait_for_done_status()
        think_result = await self._think_about_plan(goal, goal_detail, proposed_list, related_snippets)
        await self.update_plan_step(PlanStepType.THINK, {'status': ActionStatus.COMPLETED_STATUS, 'result': think_result.strip()})

        # GENERATE
        await self.update_plan_step(PlanStepType.GENERATE, {'status': ActionStatus.IN_PROGRESS_STATUS})
        await self.trigger_task_generating('start')
        task = await self._generate_task_plan(goal, goal_detail, proposed_list, related_snippets, think_result)
        await self.trigger_task_generating('end')
        await self.update_plan_step(PlanStepType.GENERATE, {'status': ActionStatus.COMPLETED_STATUS, 'result': 'Plan has been generated'})
        return task

    async def _retrieve_related_code(self, goal, goal_detail, proposed_list):
        if proposed_list:
            proposed_changes = self._format_proposed_changes(proposed_list)
            related_snippets = await self.workspace.tools.search_related_code(proposed_changes)
        else:
            related_snippets = []
        return related_snippets

    @with_general_loading(with_start=False)
    async def _think_about_plan(self, goal, goal_detail, proposed_list, related_snippets):
        system_prompt_builder = PromptBuilder(THINK_SYSTEM_PROMPT, self.workspace)
        self.system_prompt = await system_prompt_builder.format()

        user_prompt_builder = PromptBuilder(THINK_USER_PROMPT, self.workspace)
        user_prompt_builder.file_snippets = related_snippets

        combined_goal = self._build_combined_goal(goal, goal_detail, proposed_list)

        # 获取所有sys playbook + 相关 project playbook
        all_system_playbooks = self.workspace.playbook_manager.system_playbooks
        related_playbooks = await self.workspace.playbook_manager.search_playbook_from_db(text=combined_goal)
        user_prompt_builder.playbooks = all_system_playbooks + related_playbooks

        message = await user_prompt_builder.format(
            GOAL_DETAIL=combined_goal, THINK_RESULT_FORMAT=json.dumps(ThinkResult.model_json_schema(), indent=2)
        )
        think_result_model = await self.aask(
            message,
            tools=['read_playbook', 'run_cmd', 'read_file', 'search_codebase', 'read_webpage'],
            tools_callback=self.trigger_think_tooluse_callback,
            chunk_callback=self.partial_think_callback,
            post_text_validate_model=ThinkResult,
            max_tool_call_loop=15,
        )
        think_result = think_result_model.to_str()

        return think_result

    async def _generate_task_plan(self, goal, goal_detail, proposed_list, related_snippets, think_result):
        system_prompt_builder = PromptBuilder(GENERATE_SYSTEM_PROMPT, self.workspace)
        self.system_prompt = await system_prompt_builder.format()

        user_prompt_builder = PromptBuilder(GENERATE_USER_PROMPT, self.workspace)
        user_prompt_builder.file_snippets = related_snippets

        combined_goal = self._build_combined_goal(goal, goal_detail, proposed_list)

        # 获取所有sys playbook + 相关 project playbook
        all_system_playbooks = self.workspace.playbook_manager.system_playbooks
        related_playbooks = await self.workspace.playbook_manager.search_playbook_from_db(text=combined_goal)
        user_prompt_builder.playbooks = all_system_playbooks + related_playbooks

        message = await user_prompt_builder.format(
            GOAL_DETAIL=combined_goal, PRE_ANALYSIS_RESULT=think_result, TASK_FORMAT=json.dumps(TaskModel.model_json_schema(), indent=2)
        )

        task_model = await self.aask(
            message,
            chunk_callback=self.partial_task_callback,
            post_text_validate_model=TaskModel,
        )
        await self.trigger_task_generating('Validate task and refine actions')
        await asyncio.sleep(2)  # 加2秒延迟显示合并任务的信息
        verified_task = await task_model.safe_duplicate(self.workspace.tools.read_file_content, self.logger.warning)
        task = Task(**verified_task.to_dict())
        task.think_result = think_result

        return task

    async def partial_task_callback(self, task_model_str: str):
        self.partial_task_str += task_model_str
        task_model_json = await async_sanitize_generated_json(self.partial_task_str)
        # FIXME: task_model_json 有可能出错返回一个 list
        # if isinstance(task_model_json, list):
        #     task_model_json = task_model_json[0]
        if task_model_json:
            try:
                task_model = TaskModel(**task_model_json)
                await self.trigger_task_planned(task_model.to_dict())
            except (ValidationError, TypeError):
                pass

    def _format_proposed_changes(self, proposed_list):
        return ''.join(f'\n- {item}' for item in proposed_list).strip('\n')

    def _build_combined_goal(self, goal, goal_detail, proposed_list=None):
        base_goal = f'## {goal}\n\n{goal_detail}'
        if not proposed_list:
            return base_goal

        proposed_changes = self._format_proposed_changes(proposed_list)
        return f'{base_goal}\n\n{proposed_changes}'

    @with_general_loading
    async def make_additional_step(self, append_step_intent: dict):
        system_prompt_builder = PromptBuilder(ADDITIONAL_STEP_SYSTEM_PROMPT, self.workspace)
        self.system_prompt = await system_prompt_builder.format()

        user_prompt_builder = PromptBuilder(ADDITIONAL_STEP_USER_PROMPT, self.workspace)
        combined_goal = self._build_combined_goal(append_step_intent['goal'], append_step_intent['plan_draft'], [])
        content_dict = await extract_referenced_contents(append_step_intent['references'], self.workspace)
        user_prompt_builder.file_snippets = content_dict['files']
        user_prompt_builder.errors = content_dict['errors']
        user_prompt_builder.references = content_dict.get('origin_references', [])
        message = await user_prompt_builder.format(GOAL=combined_goal)
        try:
            res = await self.aask(
                message,
                images=content_dict['images'],
                tools=['run_cmd', 'read_file', 'search_codebase'],
                tools_callback=self.workspace.trigger_general_tool_callback,
                response_model=TaskStepModel,
            )
        except AgentRunException as e:
            self.logger.error(f'Make additional step failed: {e}')
            return AgentRoleNullAction()
        step_model = await res.safe_duplicate(self.workspace.tools.read_file_content, self.logger.warning)
        if not step_model:
            await self.workspace.trigger('message', 'No valid actions found, please check your request and try again.')
            return AgentRoleNullAction()
        return step_model

    async def update_plan_step(self, plan_step_type: PlanStepType, plan_step_dict):
        self.plan.update_plan_step(plan_step_type, plan_step_dict)
        await self.workspace.trigger('plan_updated', self.plan.get_plan_step_dict(plan_step_type))

    async def trigger_plan_updated(self, plan_dict: dict):
        """首次先全量更新默认值"""
        await self.workspace.trigger('plan_updated', plan_dict)

    async def trigger_task_planned(self, task_dict: dict):
        await self.workspace.trigger('task_planned', task_dict)

    async def trigger_task_generating(self, status):
        await self.workspace.trigger('tool_call_status_updated', {'tool_id': 'generate-task', 'tool_name': 'Task', 'status': status})

    async def partial_think_callback(self, partial_think_str: str):
        self.partial_think_str += partial_think_str
        # FIXME: async_sanitize_generated_json处理 {"xxx": "1.xxxx 2.xxx 3.xxx 这样子的形式有可能会从中间被截断
        think_result_json = await async_sanitize_generated_json(self.partial_think_str)
        if not think_result_json:
            return
        try:
            think_result_model = ThinkResult(**think_result_json)
            think_result_str = think_result_model.to_str()
            await self.update_plan_step(PlanStepType.THINK, {'status': ActionStatus.GENERATING, 'result': think_result_str})  # noqa
        except (ValidationError, TypeError):
            pass

    async def trigger_think_tooluse_callback(self, tool_call, status):
        if status == 'end':
            await self.workspace.trigger_general_tool_callback(tool_call, status)
