import async<PERSON>
import j<PERSON>

from typing import Optional
from heracles.agent_roles.utils import extract_user_message, llm_ability, with_general_loading
from heracles.core.schema import LLMAbilityType
from heracles.agent_roles.role_base import RoleBase
from heracles.core.schema.knowledge import RepoBasicIn<PERSON>, ProjectKnowledge
from heracles.core.utils.context_prompt_builder import PromptBuilder
from heracles.core.schema.models import PlanDraftResultModel, ResultMessageModel
from heracles.core.schema.models import EnvironmentAnalysisResultModel
from heracles.agent_workspace.workspace_analyze_item import WorkspaceAnalyzeItem
from heracles.core.utils.redis_cache import redis_cache

from .utils import (
    get_available_environments_and_middlewares,
    merge_ai_result_to_environment_list,
    merge_ai_result_to_middleware_list,
    group_and_sort_environments,
)
from .prompts import (
    PROJECT_BASE_INFO_SYSTEM_PROMPT,
    PROJECT_BASE_INFO_USER_PROMPT,
    PROJECT_KNOWLEDGE_SYSTEM_PROMPT,
    PROJECT_KNOWLEDGE_USER_PROMPT,
    PROJECT_ENVIRONMENT_SYSTEM_PROMPT,
    PROJECT_ENVIRONMENT_USER_PROMPT,
    CREATE_PROJECT_DRAFT_SYSTEM_PROMPT,
    CREATE_PROJECT_DRAFT_USER_PROMPT,
)

SYSTEM_PROMPT = """"You are the Chief Coding Architect.
You take action to explore the current directory and files to understand the project.
You are an experienced developer that is responsible for understanding a project and on users behalf."""

class AnalyzeRole(RoleBase):

    def __init__(self, workspace):
        super().__init__(workspace)
        self.system_prompt = SYSTEM_PROMPT

    def get_llm_ability(self):
        return LLMAbilityType.STRONG

    async def run(self, analyze_item_name='full'):
        if analyze_item_name == 'full':
            await self.extract_basic_info()
            await self.extract_project_knowledge()
        elif analyze_item_name == 'project_basic_info':
            await self.extract_basic_info()
        else:
            analyze_item = WorkspaceAnalyzeItem.find_by_name(self.workspace, analyze_item_name)
            if analyze_item.name == 'project_structure':
                await self._process_analyze_item(self.workspace.project_knowledge, analyze_item)
            else:
                for basic_info in self.workspace.project_knowledge.basic_info_list:
                    await self._process_analyze_item(basic_info, analyze_item)
        return self.workspace.detail_status

    def get_project_knowledge_key(self):
        project_id = self.workspace.get_project_id()
        if project_id:
            return f'project_knowledge:basic_info:{project_id}'
        else:
            return f'project_knowledge:playground:basic_info:{self.workspace.playground.playground_id}'


    async def load_runtime_environment(self):
        """加载运行环境"""
        if runtime_environment := self.workspace.playground.ide_server_client.environment:
            # FIXME: 需要改成服务端的runtime_environment加上中间件信息, 主动推送更新
            middlewares = await self.workspace.tools.list_middlewares()
            runtime_environment['middlewares'] = middlewares

            return runtime_environment

        return None


    async def load_project_knowledge_from_basic_info(self, res: RepoBasicInfo):
        # 过滤middlewares，只保留redis, mysql, postgres, mongodb四个选项
        allowed_middlewares = ['redis', 'mysql', 'postgres', 'mongodb']
        for basic_info in res.basic_info_list:
            if basic_info.middlewares:
                basic_info.middlewares = [m for m in basic_info.middlewares if m in allowed_middlewares]
        project_knowledge = ProjectKnowledge(**res.model_dump())

        return project_knowledge


    async def load_project_knowledge(self) -> Optional[ProjectKnowledge]:
        key = self.get_project_knowledge_key()

        if data := redis_cache.get(key):
            project_knowledge = ProjectKnowledge(**json.loads(data))
            if runtime_environment := await self.load_runtime_environment():
                project_knowledge.runtime_environment = runtime_environment

            return project_knowledge


        self.system_prompt = await PromptBuilder(PROJECT_BASE_INFO_SYSTEM_PROMPT, self.workspace).format()
        message = await PromptBuilder(PROJECT_BASE_INFO_USER_PROMPT, self.workspace).format()
        res = await self.aask(
            message,
            tools=["read_file"],
            response_model=RepoBasicInfo
        )
        if isinstance(res, RepoBasicInfo):
            project_knowledge = await self.load_project_knowledge_from_basic_info(res)
            redis_cache.set(key, project_knowledge.model_dump_json())
            return project_knowledge
        else:
            if res != 'LLM mock data':  # 测试模式下不打这个信息
                self.logger.warning("extract project 'project basic info' failed")
            return None


    async def extract_basic_info(self):
        """analyze and extract repo info"""

        basic_info_analyze_item = WorkspaceAnalyzeItem.find_by_name(self.workspace, "project_basic_info")
        await basic_info_analyze_item.set_running()

        project_knowledge = await self.load_project_knowledge()
        if project_knowledge:
            self.workspace.project_knowledge = project_knowledge

            await basic_info_analyze_item.set_done()
        else:
            await basic_info_analyze_item.set_failed()

    async def extract_project_knowledge(self):
        """extract project knowledge"""
        analyze_items = self.workspace.analyze_items
        tasks = []
        for analyze_item in analyze_items:
            if analyze_item.name == 'project_structure':
                tasks.append(self._process_analyze_item(self.workspace.project_knowledge, analyze_item))
            else:
                for basic_info in self.workspace.project_knowledge.basic_info_list:
                    tasks.append(self._process_analyze_item(basic_info, analyze_item))
        await asyncio.gather(*tasks)

    async def analyze_project_environment(self):
        """分析项目环境"""
        await self.trigger_analyze_project_environment_progress(
            '1/4',
            'Collecting environment list that Clacky supports...'
        )
        environment_list, middleware_list = await get_available_environments_and_middlewares()

        # 等待 basic_info 分析完成
        await self.trigger_analyze_project_environment_progress(
            '2/4',
            'Analyzing project language, framework and middlewares used...'
        )
        await self.workspace.wait_for_done_status()

        await self.trigger_analyze_project_environment_progress(
            '3/4',
            'Matching project environment and calculate compatibility...'
        )
        self.system_prompt = await PromptBuilder(PROJECT_ENVIRONMENT_SYSTEM_PROMPT, self.workspace).format()
        message = await PromptBuilder(PROJECT_ENVIRONMENT_USER_PROMPT, self.workspace).format(
            ENVIRONMENT_LIST=environment_list, MIDDLEWARE_LIST=middleware_list
        )
        result = await self.aask(
            message,
            tools=["read_file"],
            response_model=EnvironmentAnalysisResultModel
        )

        await self.trigger_analyze_project_environment_progress(
            '4/4',
            'Analysis completed'
        )

        merge_ai_result_to_environment_list(environment_list, result)
        merge_ai_result_to_middleware_list(middleware_list, result)
        environment_list = group_and_sort_environments(environment_list)

        return {
            'message_type': result.message_type,
            'reason': result.reason,
            'instructions': result.instructions,
            'environments': environment_list,
            'middlewares': middleware_list
        }

    async def _process_analyze_item(self, target_object, analyze_item):
        if analyze_item.instruction_prompt:
            await analyze_item.set_running()

            self.system_prompt = await PromptBuilder(PROJECT_KNOWLEDGE_SYSTEM_PROMPT, self.workspace).format()
            message = await PromptBuilder(PROJECT_KNOWLEDGE_USER_PROMPT, self.workspace).format()
            res = await self.aask(
                message, tools=["read_file"], response_model=ResultMessageModel
            )

            if isinstance(res, ResultMessageModel):
                if res.success:
                    setattr(target_object, analyze_item.name, res.result)
                    return await analyze_item.set_done()
                else:
                    self.logger.error(f"extract project knowledge '{analyze_item.name}' failed, reason: {res.result}")
            else:
                self.logger.error(f"extract project knowledge '{analyze_item.name}' failed, extract result: {res}")
            await analyze_item.set_failed()
        else:
            self.logger.warning(f"analyze item {analyze_item} has no instruction prompt, skipped")

    async def trigger_analyze_project_environment_progress(self, phase, message):
        await self.workspace.trigger('analyze_project_environment_progress', {
            'phase': phase,
            'message': message
        })

    @llm_ability(LLMAbilityType.FAST)
    @with_general_loading
    async def create_project_draft(self, user_input):
        """根据用户消息创建计划草稿"""
        environment_list, middleware_list = await get_available_environments_and_middlewares()
        user_message_cleaned, content_dict = await extract_user_message(user_input, self.workspace)

        self.system_prompt = await PromptBuilder(CREATE_PROJECT_DRAFT_SYSTEM_PROMPT, self.workspace).format(
            ENVIRONMENT_LIST=environment_list,
            MIDDLEWARE_LIST=middleware_list
        )
        user_message = await PromptBuilder(CREATE_PROJECT_DRAFT_USER_PROMPT, self.workspace).format(
            USER_MESSAGE=user_message_cleaned,
            IMAGES=content_dict['images'],
            WEB_PAGES=content_dict['webpages']
        )

        result = await self.aask(user_message, response_model=PlanDraftResultModel)
        merge_ai_result_to_environment_list(environment_list, result)
        merge_ai_result_to_middleware_list(middleware_list, result)
        environment_list = group_and_sort_environments(environment_list)

        return {
            'project_name': result.project_name,
            'message_type': result.message_type,
            'reason': result.reason,
            'instructions': result.instructions,
            'environments': environment_list,
            'middlewares': middleware_list
        }
