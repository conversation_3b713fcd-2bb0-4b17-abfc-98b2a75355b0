from typing import Optional

from heracles.core.exceptions import Agent<PERSON>unException
from heracles.core.logger import get_playground_logger
from heracles.core.utils import wait_for

from .observations import Observations
from .playbook_manager import PlaybookManager
from .rag_searcher import Rag<PERSON>earcher
from .tools import Tools
from .workspace_analyze_item import WorkspaceAnalyzeItem, WorkspaceStatus
from .smart_detect import SmartDetect
from .auto_fix import AutoFix
from .browser import Browser
from .lint import Lint

from heracles.core.utils.llm_function import tool_call_to_argument_pair
from heracles.core.schema.knowledge import ProjectKnowledge
from heracles.core.schema.task import Task

class AgentWorkspace:
    """记录代码的工作区信息"""

    def __init__(self, playground):
        self.playground = playground
        self.project_knowledge = ProjectKnowledge()
        self.analyze_items = [WorkspaceAnalyzeItem(self, **item_dict) for item_dict in WorkspaceAnalyzeItem.PRESETS]
        self.logger = get_playground_logger(self.playground.playground_id)
        self.smart_detect = SmartDetect(self)
        self.auto_fix = AutoFix(self)
        self.playbook_manager = PlaybookManager(self)
        self.tools = Tools(self)
        self.rag_searcher = RagSearcher(self)
        self.observations = Observations(self)
        self.task: Task | None = None

    def set_task(self, task):
        if self.task:
            raise AgentRunException('The task can only be set once')
        if not isinstance(task, Task):
            return
        self.task = task
        self.task.enable_autosave(self.playground.playground_id)

    def set_task_from_dict(self, task_dict):
        task = Task(**task_dict)
        self.set_task(task)

    def get_project_id(self) -> Optional[str]:
        return self.playground.project_id

    @property
    def status(self):
        """获取当前工作区的状态"""
        if all(status.status == WorkspaceStatus.INIT for status in self.analyze_items):
            return WorkspaceStatus.INIT
        if all(status.status in [WorkspaceStatus.DONE, WorkspaceStatus.INIT] for status in self.analyze_items):
            return WorkspaceStatus.DONE
        if any(status.status == WorkspaceStatus.RUNNING for status in self.analyze_items):
            return WorkspaceStatus.RUNNING
        return WorkspaceStatus.FAILED

    @property
    def detail_status(self):
        return {
            'status': self.status,
            'analyze_items': [analyze_item.to_dict() for analyze_item in self.analyze_items],
            'smart_detect_status': self.smart_detect.status
        }

    def ensure_basic_info_done(self):
        return self.status == WorkspaceStatus.DONE and self.analyze_items[0].status == WorkspaceStatus.DONE

    async def wait_for_done_status(self, timeout=60, interval=1):
        if self.status == WorkspaceStatus.FAILED:
            return False
        await wait_for(
            self.ensure_basic_info_done, timeout=timeout, interval=interval
        )
        return True

    async def trigger(self, event_name: str, *args, **kwargs):
        """向用户发送事件, 委托给 playground 进行操作"""
        func_call_name = f'trigger_{event_name}'
        target_method = getattr(self.playground, func_call_name)
        if not target_method:
            raise AgentRunException('trigger call error: {func_call_name} not found')
        return await target_method(*args, **kwargs)

    async def trigger_general_loading(self, tool_name, status, *, tool_id="general"):
        await self.trigger(
            "tool_call_status_updated",
            {'tool_id': tool_id, 'tool_name': tool_name, 'status': status}
        )

    async def trigger_general_tool_callback(self, tool_call, status):
        try:
            if not any(char.isupper() for char in tool_call.function.name):
                tool_name, value = tool_call_to_argument_pair(tool_call)
                await self.trigger_general_loading(tool_name, f"{tool_name}: `{value}`", tool_id=tool_call.id)
        except Exception as e:
            self.logger.error(f"Error in trigger_general_tool_callback: {e}, tool_call: {tool_call}")

    async def get_browser(self) -> Browser | None:
        url = await self.playground.ide_server_client.get_docker_url()
        if url is None:
            return None

        url = url + '/agent'

        self.logger.info(f'get_browser called, url: {url}')

        self.browser = Browser(url, self.playground.ide_server_client)
        return self.browser

    async def get_lint(self) -> Lint | None:
        url = await self.playground.ide_server_client.get_docker_url()

        if url is None:
            return None

        url = url + '/agent'

        self.logger.info(f'get_lint called, url: {url}')

        return Lint(url, self.playground.ide_server_client)
