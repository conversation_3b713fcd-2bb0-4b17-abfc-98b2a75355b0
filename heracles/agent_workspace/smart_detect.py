from typing import Literal

from heracles.core.schema import ProjectErrorMessage

class SmartDetect:
    def __init__(self, workspace):
        self.workspace = workspace
        self.errors: list[ProjectErrorMessage] = []
        self.status: Literal['init', 'task_checking', 'monitoring_errors', 'stopped'] = 'init'

    async def set_status(self, status: Literal['task_checking', 'monitoring_errors', 'stopped']):
        self.status = status

    def get_errors(self, category: Literal['terminal', 'browser_console', 'lint'] | None = None):
        if category:
            return [error for error in self.errors if error.category == category and error.category != 'screenshot']
        return self.errors

    def clear_errors(self):
        self.errors.clear()
        # TODO: 通知前端清空
        # self.workspace.trigger('smart_detect_errors_cleared')

    def get_screenshot_error_urls(self):
        return [error.content for error in self.errors if error.category == 'screenshot']
