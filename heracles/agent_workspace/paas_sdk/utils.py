import time
import random
import string
from Crypto.Cipher import AES
import base64
import aiohttp
from typing import TypedDict, Any, Dict, Optional
from heracles.core.config import get_env_var
from heracles.core.logger import heracles_logger as logger


PAAS_DOMAIN_URL = get_env_var('PAAS_DOMAIN_URL', must=True)
PAAS_TENANT_CODE = get_env_var('PAAS_TENANT_CODE', must=True)


class Result(TypedDict):
    success: bool
    data: Any
    error: Any


class Template(TypedDict):
    id: str
    name: str


class ImportRepositoryInfo(TypedDict):
    environmentVerId: str
    owner: str
    repo: str
    ref: str
    username: str
    purpose: str
    token: str
    privateKey: str


class TicketInfo(TypedDict):
    ticketId: str
    tenantCode: str


async def fetch(
    session: aiohttp.ClientSession,
    url: str,
    method: str = 'GET',
    headers: Optional[dict] = None,
    json: Optional[dict] = None,
    data: Any = None,
) -> Result:
    # 记录请求开始时间
    start_time = time.time()

    # 获取当前时间戳
    timestamp = str(int(start_time * 1000))
    # 生成 nonce
    nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    # 生成 token
    token = generate_token(nonce, timestamp)

    # 插入 tenantCode, nonce, timestamp 和 token 到 headers
    if headers is None:
        headers = {}
    headers.update(
        {'Content-Type': 'application/json', 'tenantCode': PAAS_TENANT_CODE, 'nonce': nonce, 'timestamp': timestamp, 'token': token}
    )
    full_url = PAAS_DOMAIN_URL + url

    try:
        async with session.request(method, full_url, headers=headers, json=json, data=data) as response:
            if response.status == 200:
                res = await response.json()
                if 'data' in res and 'status' not in res['data']:
                    result = Result(success=True, data=res['data'], error=None)
                elif 'status' in res and res['status'] == 'success':
                    result = Result(success=True, data=res, error=None)
                else:
                    result = Result(success=False, data=res, error=None)
            elif response.status == 201:
                res = await response.json()
                if 'data' in res:
                    result = Result(success=True, data=res['data'], error=None)
                elif 'status' in res and res['status'] == 'success':
                    result = Result(success=True, data=res, error=None)
                else:
                    result = Result(success=False, data=res, error=None)
            elif response.status == 401:
                result = Result(success=False, data={}, error='Unauthorized: Check your token and tenant code.')
            elif response.status == 403:
                result = Result(success=False, data={}, error="Forbidden: You don't have permission to perform this action.")
            elif response.status == 404:
                result = Result(success=False, data={}, error='Not Found: The specified resource does not exist.')
            elif response.status == 504:
                result = Result(success=False, data={}, error='Gateway Timeout: The server is taking too long to respond.')
            else:
                result = Result(success=False, data={}, error=f'Unexpected status code: {response.status}')
    except Exception as e:
        result = Result(success=False, data={}, error=f'Request failed: {str(e)}')
    finally:
        # 计算请求耗时并打印
        end_time = time.time()
        duration = (end_time - start_time) * 1000  # 转换为毫秒
        logger.info(f"request - {method} {full_url} - cost time: {duration:.2f}ms")

    return result


def generate_token(nonce: str, timestamp: str) -> str:
    """Generate token for PAAS API authentication

    Returns:
        str: The generated token string
    """
    # Secret key for encryption
    key = 'demosecret123456'.encode('utf-8')
    # Create message
    msg = f'{PAAS_TENANT_CODE}_{nonce}_{timestamp}'.encode('utf-8')

    # Pad message to be multiple of 16 bytes
    pad_len = 16 - (len(msg) % 16)
    msg = msg + bytes([pad_len]) * pad_len

    # Encrypt using AES ECB mode
    cipher = AES.new(key, AES.MODE_ECB)
    encrypted = cipher.encrypt(msg)
    token = base64.b64encode(encrypted).decode()

    return token


async def get_environments():
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            # '/api/v1/sdk/environments'
            '/api/v1/sdk/environmentVersions',
        )


async def get_template_list() -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(session, '/thread/template/list')


async def get_thread_template_list() -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(session, '/thread/template/list')


async def import_repository(repository_info: ImportRepositoryInfo) -> Result:
    username = repository_info['username']
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            '/api/v1/sdk/codeZones/github',
            method='POST',
            headers={
                'userId': username,
            },
            json=dict(repository_info),
        )


async def import_repository_async(repository_info: ImportRepositoryInfo) -> Result:
    username = repository_info['username']
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            '/api/v1/sdk/codeZones/github/async',
            method='POST',
            headers={
                'userId': username,
            },
            json=dict(repository_info),
        )


async def import_repository_task_check(task_id: str) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            f'/api/v1/sdk/codeZones/github/{task_id}',
        )


async def fork_codezone(codezone_id: str) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            '/api/v1/sdk/codeZones/fork',
            method='POST',
            json={
                'codeZoneId': codezone_id,
                'purpose': 2,  # for llm test mark
                'commitId': '',
                'withMiddlewareData': True,
            },
        )


async def upgrade_codezone_environment(codezone_id: str, environment_ver_code: str) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            f'/api/v1/sdk/codeZones/{codezone_id}/upgrade',
            method='PUT',
            json={
                'environmentVerCode': environment_ver_code,
            },
        )


async def get_playground_id(codezone_id: str) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(session, f'/api/v1/sdk/codeZones/{codezone_id}/playground')


async def get_ticket(ticket_info: TicketInfo) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            '/api/v1/sdk/ticket',
            method='POST',
            json=dict(ticket_info),
        )


async def async_delete_codezone(codezone_id: str) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            f'/api/v1/sdk/codeZones/{codezone_id}',
            method='DELETE',
            json={
                'codeZoneId': codezone_id,
                'commitId': '',
                'withMiddlewareData': True,
            },
        )


async def delete_codezone(codezone_id: str) -> Result:
    """这里实现了一个同步的接口"""
    async with aiohttp.ClientSession() as session:
        json_data = {
            'codeZoneId': codezone_id,
            'commitId': '',
            'withMiddlewareData': True,
        }
        return await fetch(session, f'/api/v1/sdk/codeZones/{codezone_id}', method='DELETE', json=json_data)


async def create_codezone(environment_ver_id: str) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            '/api/v1/sdk/codeZones',
            method='POST',
            json={'environmentVerId': environment_ver_id, 'unitTestFrameworkId': ''},
        )


async def upload_files(codezone_id: str, files: Dict[str, Any]) -> Any:
    """only support upload zip files: {'file1': 'abc.zip', 'file2'...}"""
    form_data = aiohttp.FormData()
    for key, file_path in files.items():
        form_data.add_field(name=key, value=open(file_path, 'rb'), filename=file_path)

    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            f'/api/v1/sdk/codeZones/{codezone_id}/importFiles',
            method='POST',
            data=form_data,
        )


async def git_clone(environment_ver_id: str, owner: str, username: str, repo: str) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            '/api/v1/sdk/codeZones/github',
            method='POST',
            json={
                'environmentVerId': environment_ver_id,
                'owner': owner,
                'username': username,
                'repo': repo,
                'ref': '',
                'token': '',
                'unitTestFrameworkId': '',
            },
        )


async def bind_playground_info(playground_id: str) -> Result:
    """get codezone id by playground"""
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            f'/api/v1/sdk/playgrounds/{playground_id}/bindInfo',
            json={
                'bindType': 'CODE_ZONE',
                'codeZoneId': playground_id,
            },
        )


async def import_codezone_file(codezone_id: str, file_name: str, file_content: str) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            '/api/v1/sdk/codeZones/import',
            method='POST',
            json={'codeZoneId': codezone_id, 'codeZoneImportFileListDTOList': [{'fileContent': file_content, 'fileName': file_name}]},
        )


async def get_playground_info(playground_id: str) -> Result:
    """Get codezone id by playground"""
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            f'/api/v1/sdk/playgrounds/{playground_id}/bindInfo',
            json={
                'bindType': 'CODE_ZONE',
                'codeZoneId': playground_id,
            },
        )


async def add_middleware_to_codezone(codezone_id: int, middleware_define_id: int) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(session, f'/api/v1/sdk/codeZones/{codezone_id}/middlewareDefines/{middleware_define_id}', method='POST')

async def remove_middleware_from_codezone(codezone_id: int, middleware_define_id: int) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(session, f'/api/v1/sdk/codeZones/{codezone_id}/middlewareDefines/{middleware_define_id}', method='DELETE')

async def list_middlewares_of_codezone(codezone_id: int) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            f'/api/v1/sdk/codeZones/{codezone_id}/middlewareDefines/config',
        )


async def get_paas_middlewares() -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(
            session,
            '/api/v1/sdk/middlewares',
        )


async def bind_middleware_to_codezone(codezone_id: int, middleware_name: str):
    middlewares = await get_paas_middlewares()
    for middleware in middlewares['data']:
        if middleware_name.lower() in middleware['name'].lower():
            logger.debug(f'middleware data: {middleware}')
            return await add_middleware_to_codezone(codezone_id, middleware['id'])

async def unbind_middleware_from_codezone(codezone_id: int, middleware_name: str):
    middlewares = await get_paas_middlewares()
    for middleware in middlewares['data']:
        if middleware_name.lower() in middleware['name'].lower():
            logger.debug(f'middleware data: {middleware}')
            return await remove_middleware_from_codezone(codezone_id, middleware['id'])

async def stop_playground(playground_id: int) -> Result:
    async with aiohttp.ClientSession() as session:
        return await fetch(session, f'/api/v1/sdk/playgrounds/{playground_id}/pause', method='POST')


async def fork_new_codezone_get_id(codezone_id):
    code_zone_response = await fork_codezone(codezone_id)
    try:
        new_codezone_id = code_zone_response['data']['id']
        return new_codezone_id
    except Exception as error:
        raise Exception(f'fork codezone error {code_zone_response}') from error


async def get_playground_id_from_codezone_id(codezone_id):
    response = await get_playground_id(codezone_id)
    return response['data']['id']


async def fork_code_zone_get_id(codezone_id):
    code_zone_response = await fork_codezone(codezone_id)
    return code_zone_response['data']['id']
