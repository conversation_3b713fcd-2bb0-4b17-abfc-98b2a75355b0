from heracles.core.utils.template_registry import GL<PERSON><PERSON>L_PRE_PROMPT
from jinja2 import Environment
from string import Formatter
from collections import defaultdict
import difflib

from heracles.agent_workspace.playbook import Playbook
from heracles.agent_workspace.workspace_analyze_item import WorkspaceStatus, WorkspaceAnalyzeItem
from heracles.core.schema import FileSnippet, ProjectErrorMessage
from heracles.core.schema.task import FileActionObject

from heracles.core.exceptions import IDEServerFileNotFoundError


class PromptBuilder:
    """用于构建上下文提示的类"""

    def __init__(self, template: str, workspace):
        self.template = template
        self.workspace = workspace
        self.file_snippets: list[FileSnippet] = []
        self.playbooks: list[Playbook] = []
        self.errors: list[ProjectErrorMessage] = []
        self.references: list[str] = []
        self.webpages: list[str] = []
        self.images: list[str] = []
        self._template_placeholders = {name for _, name, _, _ in Formatter().parse(template) if name is not None}
        self.clacky_rules_path = '.clacky<PERSON>les'
        self._last_action_cache: dict[str, int] = {}  # {file_path: action_id}
        self._last_diff_cache: dict[str, str] = {}    # {file_path: diff_str}

        # 创建环境时添加 isinstance
        env = Environment()
        env.globals['isinstance'] = isinstance

    async def convert_file_tree(self,):
        file_list = await self.workspace.tools.file_tree()

        return await GLOBAL_PRE_PROMPT.get("FILE_TREE").render(file_list=file_list)

    async def format(self, **kwargs) -> str:
        context = kwargs
        if "FILE_TREE" in self._template_placeholders:
            context['FILE_TREE'] = await self.convert_file_tree()

        if any(item.upper() in self._template_placeholders for item in [
            "project_basic_info",
            "project_structure",
            "project_components",
            "project_dependencies"
        ]):
            current_middlewares = await self.workspace.tools.list_middlewares()

        for item in [
            "project_basic_info",
            "project_structure",
            "project_components",
            "project_dependencies"
        ]:
            if item.upper() in self._template_placeholders:
                context[item.upper()] = ""
                analyze_item = WorkspaceAnalyzeItem.find_by_name(self.workspace, item)
                if analyze_item and analyze_item.status == WorkspaceStatus.DONE:
                    context[item.upper()] = await (GLOBAL_PRE_PROMPT.get("PROJECT_KNOWLEDGE")
                                                   .render(
                                                        item=analyze_item,
                                                        workspace=self.workspace,
                                                        current_middlewares=current_middlewares
                                                   ))

        if "CLACKY_RULES" in self._template_placeholders:
            context['CLACKY_RULES'] = ""
            try:
                file_content = await self.workspace.tools.read_file_content(self.clacky_rules_path, silent=True)
                context['CLACKY_RULES'] = await (GLOBAL_PRE_PROMPT.get("CLACKY_RULES")
                                                 .render(clacky_rules=file_content.strip()))
            except IDEServerFileNotFoundError as e:
                self.workspace.logger.warning(str(e))
            except Exception as e:
                self.workspace.logger.warning('Update project knowledge failed: %s', str(e))

        if 'FILE_SNIPPETS' in self._template_placeholders:
            context['FILE_SNIPPETS'] = ''
            if self.file_snippets:
                snippet_dict: dict[str, FileSnippet] = {}
                # 1. 按 path 分组 并处理 row_start 和 row_end
                path_groups = defaultdict(list)
                for snippet in self.file_snippets:
                    if snippet.row_start == -1 or snippet.row_end == -1:
                        snippet.row_start = 1
                        snippet.row_end = len(snippet.content.split('\n'))
                    if len(snippet.content.split('\n')) > snippet.row_end - snippet.row_start:
                        snippet.row_end = snippet.row_start + len(snippet.content.split('\n')) - 1
                    path_groups[snippet.path].append(snippet)
                # 2. 对每组做区间合并（去除被包含的）
                unique_snippets = []
                for _, group in path_groups.items():
                    # 按 row_start 升序，row_end 降序 同一start, end较大的在前
                    group.sort(key=lambda s: (s.row_start, -s.row_end))
                    merged: list[FileSnippet] = []
                    for snippet in group:
                        if not merged or snippet.row_start > merged[-1].row_end or snippet.row_end > merged[-1].row_end:
                            merged.append(snippet)
                        else:
                            merged[-1].instructions.extend(snippet.instructions)
                    unique_snippets.extend(merged)
                # 3. 生成 key，避免重复
                for snippet in unique_snippets:
                    key = f'{snippet.path}:{snippet.row_start}:{snippet.row_end}'
                    if key in snippet_dict:
                        snippet_dict[key].instructions.extend(snippet.instructions)
                    else:
                        snippet_dict[key] = snippet
                context['FILE_SNIPPETS'] = await GLOBAL_PRE_PROMPT.get('FILE_SNIPPETS').render(snippets=list(snippet_dict.values())) #noqa

        if "PLAYBOOKS" in self._template_placeholders:
            context['PLAYBOOKS'] = ""
            if self.playbooks:
                for playbook in self.playbooks:
                    playbook.content = await playbook.render_content(self.workspace)
                context['PLAYBOOKS'] = await GLOBAL_PRE_PROMPT.get("PLAYBOOKS").render(playbooks=self.playbooks)

        if "PLAYBOOK_LIST" in self._template_placeholders:
            context['PLAYBOOK_LIST'] = ""
            if self.playbooks:
                playbooks = [
                    str(playbook.dict())
                    for playbook in self.playbooks
                ]
                context['PLAYBOOK_LIST'] = await GLOBAL_PRE_PROMPT.get("PLAYBOOK_LIST").render(playbooks=playbooks)

        if "WEB_PAGES" in self._template_placeholders:
            context['WEB_PAGES'] = ""
            if self.webpages:
                context['WEB_PAGES'] = await GLOBAL_PRE_PROMPT.get("WEB_PAGES").render(webpages=self.webpages)

        if "IMAGES" in self._template_placeholders:
            context['IMAGES'] = ""
            if self.images:
                context['IMAGES'] = await GLOBAL_PRE_PROMPT.get("IMAGES").render(images=self.images)

        if "ERRORS" in self._template_placeholders:
            context['ERRORS'] = ""
            if self.errors:
                context['ERRORS'] = await GLOBAL_PRE_PROMPT.get("ERRORS").render(errors=self.errors)

        if "TASK" in self._template_placeholders:
            context['TASK'] = ""
            if self.workspace.task and not self.workspace.playground.is_root:
                context['TASK'] = await GLOBAL_PRE_PROMPT.get("TASK").render(task=self.workspace.task)

        if "IN_PROGRESS_TASK_ACTION" in self._template_placeholders:
            context['IN_PROGRESS_TASK_ACTION'] = ""
            if self.workspace.task:
                action = self.workspace.task.get_next_runnable_action()
                if action and isinstance(action.action_object, FileActionObject):
                    context['IN_PROGRESS_TASK_ACTION'] = await (GLOBAL_PRE_PROMPT.get("IN_PROGRESS_TASK_ACTION")
                                                                .render(task_action=action))

        if "RECENT_FILE_CHANGES" in self._template_placeholders:
            context['RECENT_FILE_CHANGES'] = ""
            if self.workspace.task:
                # 取每个文件最新的 file action
                last_file_action_per_path = {}
                first_file_action_per_path = {}
                for step in self.workspace.task.task_steps:
                    for action in step.task_actions:
                        if action.action in ['add_file', 'modify_file'] and action.status == 'completed':
                            file_path = action.action_object.path
                            # 记录最新的 action
                            last_file_action_per_path[file_path] = action
                            # 记录第一个 action
                            if file_path not in first_file_action_per_path:
                                first_file_action_per_path[file_path] = action

                file_diffs = {}
                for file_path, last_action in last_file_action_per_path.items():
                    action_id = last_action.id
                    # 检查缓存
                    if self._last_action_cache.get(file_path) == action_id:
                        # 没有变化，直接用缓存
                        file_diffs[file_path] = self._last_diff_cache.get(file_path, "")
                        continue

                    # 获取第一个 action 的 snapshot
                    first_action = first_file_action_per_path[file_path]
                    snapshot_uuid = first_action.action_object.snapshot_uuid
                    try:
                        snapshot_content = await self.workspace.tools.query_snapshot_file_by_uuid(
                            file_path, snapshot_uuid
                        )
                        current_content = await self.workspace.tools.read_file_content(file_path, silent=True)
                        diff = list(difflib.unified_diff(
                            snapshot_content.splitlines(),
                            current_content.splitlines(),
                        ))
                        diff_str = "\n".join(diff)
                        file_diffs[file_path] = diff_str
                        # 更新缓存 - 使用最新的 action_id 作为标识
                        self._last_action_cache[file_path] = action_id
                        self._last_diff_cache[file_path] = diff_str
                    except Exception as e:
                        self.workspace.logger.warning(f"Failed to generate diff for {file_path}: {e}")
                        file_diffs[file_path] = f"Failed to generate diff: {e}"

                if file_diffs:
                    context['RECENT_FILE_CHANGES'] = await (GLOBAL_PRE_PROMPT.get("RECENT_FILE_CHANGES")
                                                             .render(diffs=file_diffs))

        if "REFERENCES" in self._template_placeholders:
            context['REFERENCES'] = ""
            if self.references:
                context['REFERENCES'] = await GLOBAL_PRE_PROMPT.get("REFERENCES").render(references=self.references)

        return self.template.format(**context)
