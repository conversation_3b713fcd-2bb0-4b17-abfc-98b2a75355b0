import aiohttp
import socketio  # type: ignore
import os
import inspect
import traceback

from pydantic import ValidationError
from functools import partial

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles

from heracles.core.utils.auth import verify_jwt_token
from heracles.server.clacky.playground_manager import PlaygroundManager
from heracles.server.clacky.playground_channel import PlaygroundChannel

from heracles.core.logger import heracles_logger as logger, get_playground_logger

from heracles.core.utils.string import underscore, extract_query_param
from heracles.core.exceptions import UserException, AgentException, IDEServerException, IDEServerConnectException
from heracles.core.config import get_env_var

socketio_server = socketio.AsyncServer(async_mode='asgi', cors_allowed_origins='*', transports=['websocket'])
playground_manager = PlaygroundManager()

static_dir = os.path.join(os.path.dirname(__file__), 'static')
# set aiohttp client timeout
aiohttp.ClientTimeout(total=10)

class StaticFilesWithoutCaching(StaticFiles):  # pragma: no cover
    async def get_response(self, path: str, scope):
        response = await super().get_response(path, scope)
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response


_app = FastAPI()
_app.mount('/static', StaticFilesWithoutCaching(directory=static_dir), name='static')
_app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)

@_app.get('/demo')
async def index():
    with open(os.path.join(static_dir, 'index.html')) as f:
        return HTMLResponse(content=f.read())

@_app.get('/health')
async def health_check():
    return 'OK'

@_app.on_event('shutdown')
async def shutdown_event():
    logger.debug("shutdown")

@_app.on_event("startup")
async def startup_event():
    logger.debug("startup")
    playground_manager.start_cleanup_playground_task()

    if get_env_var('ADMIN_VECTOR_DB_HOST_OPTIONAL'):
        from .admin.admin import app as app_admin, load_model_and_db
        await load_model_and_db()
        _app.mount("/api/admin", app_admin)


app = socketio.ASGIApp(socketio_server, _app)

@socketio_server.event
async def connect(sid, environ, auth: dict):
    query_string = environ.get('QUERY_STRING')
    playground_id = extract_query_param(query_string, 'playgroundId')

    if auth is None:
        raise socketio.exceptions.ConnectionRefusedError(f'Auth data error, sid={sid}')

    if playground_id is None:
        if 'playgroundId' not in auth:
            raise socketio.exceptions.ConnectionRefusedError(f'Auth data error, sid={sid}')
        playground_id = auth['playgroundId']

    if verify_jwt_token(auth.get('token')) is False:
        raise socketio.exceptions.ConnectionRefusedError(f'Auth data error, sid={sid}')

    logger = get_playground_logger(playground_id)
    logger.info(f'new connection: auth={auth} sid={sid}')

    isRoot = auth.get('isRoot') or False
    project_id = auth.get('projectId', None)
    playground_channel = PlaygroundChannel(sid, socketio_server, playground_manager, playground_id, project_id, isRoot)
    await socketio_server.save_session(sid, {'playground_channel': playground_channel})
    try:
        await playground_channel.start()
        logger.info(f'Playground {playground_id} channel {sid} is running')
    except IDEServerConnectException as e:
        logger.error(f'Connect IDEServer error {e} Traceback: {traceback.format_exc()}')
        raise socketio.exceptions.ConnectionRefusedError(f'Connecting to IDEServer fail, {str(e)}') from None
    except Exception as e:
        logger.error(f'Fatal error {e} Traceback: {traceback.format_exc()}')
        raise socketio.exceptions.ConnectionRefusedError(f'Fatal error from agent, {str(e)}') from None
    if playground_channel.project_id != playground_channel.current_playground.project_id:
        await playground_channel.transmit('projectIdMismatch', f"Project id mismatch. sid={sid}, playground_id={playground_id}")


@socketio_server.on('*')
async def on_any(event_name, sid, *args):
    def _deal_event_res(status, message='', data=None):
        res = {'status': status, 'message': str(message), 'data': data}
        logger.info(f'u<<: {event_name}, {res}')
        return res
    session = await socketio_server.get_session(sid)
    playground_channel = session['playground_channel']
    logger = get_playground_logger(playground_channel.playground_id)
    logger.info(f'u>>: {event_name}, {args}')
    is_on_any = False
    if playground_channel.project_id != playground_channel.current_playground.project_id:
        await playground_channel.transmit('projectIdMismatch', f"Project id mismatch. sid={sid}, playground_id={playground_channel.playground_id}") # noqa
        # return _deal_event_res('fail', message='Project id mismatch.')

    if event_name == 'message':
        return await playground_channel.on_message(*args)

    if hasattr(playground_channel, f'on_{underscore(event_name)}'):
        event_callback = getattr(playground_channel, f'on_{underscore(event_name)}')
    else:
        is_on_any = True
        event_callback = partial(playground_channel.on_any, event_name)

    # 获取参数个数
    params = inspect.signature(event_callback).parameters
    params_len = len(params)
    default_params_len = sum(1 for param in params.values() if param.default != param.empty)
    if not is_on_any and (len(args) < params_len - default_params_len or len(args) > params_len):
        if default_params_len > 0:
            message = f'UserEventArgumentException: need {params_len - default_params_len}~{params_len} but given {len(args)}'
        else:
            message = f'UserEventArgumentException: need {params_len} but given {len(args)}'
        return _deal_event_res('fail', message=message, data={'exception_name': 'UserEventArgumentException'})
    try:
        if params_len == 0:
            res = await event_callback()
        else:
            res = await event_callback(*args)
    except ValidationError as e:
        # Format validation errors in a cleaner way
        error_details = []
        for error in e.errors():
            field = ".".join(str(x) for x in error["loc"])
            error_details.append(f"- {field}: {error['msg']}")
        error_msg = "Validation Error:\n" + "\n".join(error_details)
        return _deal_event_res('fail', message=error_msg, data={'exception_name': 'ValidationError'})
    except (UserException, AgentException, IDEServerException) as e:
        logger.warning(f'Normal error occurred while processing the user event: `{event_name}` \nTraceback: {traceback.format_exc()}')
        return _deal_event_res('fail', message=e, data={'exception_name': type(e).__name__})
    except Exception as e:
        # 这里一般是代码错误引起, 需要记录错误日志
        logger.error(f'Fatal error occurred while processing the user event: `{event_name}` \nTraceback: {traceback.format_exc()}')
        return _deal_event_res('fatal', message=e, data={'exception_name': type(e).__name__})
    return _deal_event_res('ok', data=res)

@socketio_server.event
async def disconnect(sid):
    logger.info(f'get client disconnect event: sid={sid}')

    try:
        session = await socketio_server.get_session(sid)
        if session is None:
            return

        playground_channel = session['playground_channel']
        if playground_channel is None:
            return

        await playground_channel.stop()

        playground_logger = get_playground_logger(playground_channel.playground_id)
        playground_logger.info(f'channel disconnected: sid={sid}')

    except Exception as e:
        logger.warning(f'Error in disconnect handler: {str(e)}, Traceback: {traceback.format_exc()}')
