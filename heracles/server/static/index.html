<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Socket.IO Test</title>
  <link rel="stylesheet" href="static/index.css">
</head>

<body>
  <div class="agent-demo-index">
    <div class="agent-demo-index-wrapper">
      <h1>Hello Clacky Agent</h1>
      <label id='status'>not connected</label>

      <form class="playground-id-form" onsubmit="onPlaygroundIdSubmit(event)">
        <input id="playground-id-input" type="text" placeholder="Please input playground id" required />
        <label>
        <input type="checkbox" id="is-root-checkbox" />
          Is Root
        </label>
        <button type="submit" id="connect">connect</button>
      </form>

      <div class="flex flex-col hidden gap-4 items-center w-full" id="available-content">
        <div class="chat-history" id="chat-history">
        </div>
        <div class="button-group">
          <button type="button" id="reset-session-btn" data-event="uResetMessageSession">重置会话</button>
          <button type="button" id="start-task-btn" data-event="uStartTask">开始任务</button>
          <button type="button" id="reset-task-btn" data-event="uResetTask">重置任务</button>
          <button type="button" id="enable-smart-detect-btn" data-event="uEnableSmartDetect">启用智能检测</button>
          <button type="button" id="disable-smart-detect-btn" data-event="uDisableSmartDetect">停止智能检测</button>
          <button type="button" id="start-auto-fix-btn" data-event="uStartAutoFix">启动自动修复</button>
        </div>
        <form class="chat-box" onsubmit="onFormSubmitClick(event)">
          <select class="chat-box--select" id="chat-box--select">
            <option value="message" default>Message 发送消息</option>
            <option value="uMakeSpec">uMakeSpec 开始需求分析</option>
            <option value="uMakePlan">uMakePlan 计划任务</option>
            <option value="uStartTask">uStartTask 开始任务</option>
            <option value="uPauseTask">uPauseTask 暂停任务</option>
            <option value="uResumeTask">uResumeTask 恢复任务</option>
            <option value="uCancelTask">uCancelTask 取消任务</option>
            <option value="uResetTask">uResetTask 重置清除任务</option>
            <option value="uAddStep">uAddStep 追加步骤</option>
            <option value="uAddAction">uAddAction 追加动作</option>
            <option value="uModifyStep">uModifyStep 更新步骤</option>
            <option value="uModifyAction">uModifyAction 更新动作</option>
            <option value="uToggleRevertAction">uToggleRevertAction 切换回滚/恢复动作</option>
            <option value="uDeleteStep">uDeleteStep 删除步骤</option>
            <option value="uDeleteAction">uDeleteAction 删除动作</option>
            <option value="uRerunTaskAction">uRerunTaskAction 重新执行指定动作</option>
            <option value="uBuildWorkspace">uBuildWorkspace 构建工作空间</option>
            <option value="uResetMessageSession">uResetMessageSession 重置聊天会话</option>
            <option value="uEnableSmartDetect">uEnableSmartDetect 启用智能检测</option>
            <option value="uDisableSmartDetect">uDisableSmartDetect 停止智能检测</option>
            <option value="uCmdK">uCmdK</option>
            <option value="uCodeCompletion">uCodeCompletion</option>
            <option value="uMakeCommitFileList">uMakeCommitFileList</option>
            <option value="uMakeCommitMessage">uMakeCommitMessage</option>
            <option value="uMakePRFileList">uMakePRFileList</option>
            <option value="uMakePRMessage">uMakePRMessage</option>
            <option value="uRunCmd">uRunCmd</option>
            <option value="remessage">Message 重新发送消息</option>
            <option value="uStopMessage">Message 停止发送消息</option>
            <option value="uAnalyzeProjectEnvironment">uAnalyzeProjectEnvironment 分析项目环境</option>
            <option value="uRefinePrompt">uRefinePrompt 润色提示词 </option>
            <option value="uStartAutoFix">uStartAutoFix 启动自动修复</option>
          </select>
          <textarea class="chat-box--textarea" id="chat-box--textarea" wrap="hard" autofocus></textarea>
          <button type="submit" class="chat-box--btn">Send</button>
        </form>
      </div>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script type="module" src="static/index.js"></script>
</body>

</html>
